import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Card,
  Modal,
  Form,
  InputNumber,
  Switch,
  message,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Option } = Select;

interface Quiz {
  id: number;
  title: string;
  category: string;
  questionCount: number;
  estimatedTime: number;
  difficulty: 'easy' | 'medium' | 'hard';
  isFree: boolean;
  isFeatured: boolean;
  status: 'active' | 'inactive';
  viewCount: number;
  takeCount: number;
  createdAt: string;
}

const QuizManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingQuiz, setEditingQuiz] = useState<Quiz | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  const [quizzes, setQuizzes] = useState<Quiz[]>([
    {
      id: 1,
      title: 'MBTI人格测试',
      category: '人格测试',
      questionCount: 60,
      estimatedTime: 15,
      difficulty: 'medium',
      isFree: true,
      isFeatured: true,
      status: 'active',
      viewCount: 1234,
      takeCount: 456,
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      title: '情商测试',
      category: '情商测试',
      questionCount: 40,
      estimatedTime: 10,
      difficulty: 'easy',
      isFree: true,
      isFeatured: true,
      status: 'active',
      viewCount: 987,
      takeCount: 234,
      createdAt: '2024-01-10'
    },
    {
      id: 3,
      title: '职业兴趣测试',
      category: '职业测试',
      questionCount: 30,
      estimatedTime: 8,
      difficulty: 'easy',
      isFree: true,
      isFeatured: false,
      status: 'active',
      viewCount: 654,
      takeCount: 189,
      createdAt: '2024-01-08'
    }
  ]);

  const columns: ColumnsType<Quiz> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '测评标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '题目数',
      dataIndex: 'questionCount',
      key: 'questionCount',
      width: 80,
      render: (count) => `${count}题`
    },
    {
      title: '预估时间',
      dataIndex: 'estimatedTime',
      key: 'estimatedTime',
      width: 90,
      render: (time) => `${time}分钟`
    },
    {
      title: '难度',
      dataIndex: 'difficulty',
      key: 'difficulty',
      width: 80,
      render: (difficulty: 'easy' | 'medium' | 'hard') => {
        const colorMap: Record<'easy' | 'medium' | 'hard', string> = { easy: 'green', medium: 'orange', hard: 'red' };
        const textMap: Record<'easy' | 'medium' | 'hard', string> = { easy: '简单', medium: '中等', hard: '困难' };
        return <Tag color={colorMap[difficulty]}>{textMap[difficulty]}</Tag>;
      }
    },
    {
      title: '类型',
      key: 'type',
      width: 100,
      render: (_, record) => (
        <Space>
          {record.isFree && <Tag color="green">免费</Tag>}
          {record.isFeatured && <Tag color="gold">推荐</Tag>}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '浏览/完成',
      key: 'stats',
      width: 100,
      render: (_, record) => (
        <div>
          <div>{record.viewCount}</div>
          <div style={{ color: '#8c8c8c', fontSize: '12px' }}>{record.takeCount}</div>
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个测评吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingQuiz(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (quiz: Quiz) => {
    setEditingQuiz(quiz);
    form.setFieldsValue(quiz);
    setModalVisible(true);
  };

  const handleView = (quiz: Quiz) => {
    message.info(`查看测评: ${quiz.title}`);
  };

  const handleDelete = (id: number) => {
    setQuizzes(quizzes.filter(quiz => quiz.id !== id));
    message.success('删除成功');
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (editingQuiz) {
        // 编辑
        setQuizzes(quizzes.map(quiz => 
          quiz.id === editingQuiz.id ? { ...quiz, ...values } : quiz
        ));
        message.success('更新成功');
      } else {
        // 新增
        const newQuiz: Quiz = {
          id: Date.now(),
          ...values,
          viewCount: 0,
          takeCount: 0,
          createdAt: new Date().toISOString().split('T')[0]
        };
        setQuizzes([...quizzes, newQuiz]);
        message.success('创建成功');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          测评管理
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{
            background: 'linear-gradient(135deg, #E6D4EF 0%, #D1E5E9 100%)',
            border: 'none'
          }}
        >
          新增测评
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Space>
          <Input
            placeholder="搜索测评标题"
            prefix={<SearchOutlined />}
            style={{ width: 200 }}
          />
          <Select placeholder="选择分类" style={{ width: 120 }}>
            <Option value="">全部</Option>
            <Option value="人格测试">人格测试</Option>
            <Option value="情商测试">情商测试</Option>
            <Option value="职业测试">职业测试</Option>
            <Option value="恋爱测试">恋爱测试</Option>
          </Select>
          <Select placeholder="选择状态" style={{ width: 120 }}>
            <Option value="">全部</Option>
            <Option value="active">启用</Option>
            <Option value="inactive">禁用</Option>
          </Select>
          <Button type="primary">搜索</Button>
        </Space>
      </Card>

      {/* 测评列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={quizzes}
          rowKey="id"
          pagination={{
            total: quizzes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingQuiz ? '编辑测评' : '新增测评'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            difficulty: 'easy',
            isFree: true,
            isFeatured: false,
            status: 'active'
          }}
        >
          <Form.Item
            name="title"
            label="测评标题"
            rules={[{ required: true, message: '请输入测评标题' }]}
          >
            <Input placeholder="请输入测评标题" />
          </Form.Item>

          <Form.Item
            name="category"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select placeholder="请选择分类">
              <Option value="人格测试">人格测试</Option>
              <Option value="情商测试">情商测试</Option>
              <Option value="职业测试">职业测试</Option>
              <Option value="恋爱测试">恋爱测试</Option>
              <Option value="心理健康">心理健康</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="questionCount"
            label="题目数量"
            rules={[{ required: true, message: '请输入题目数量' }]}
          >
            <InputNumber min={1} max={200} placeholder="请输入题目数量" />
          </Form.Item>

          <Form.Item
            name="estimatedTime"
            label="预估时间（分钟）"
            rules={[{ required: true, message: '请输入预估时间' }]}
          >
            <InputNumber min={1} max={120} placeholder="请输入预估时间" />
          </Form.Item>

          <Form.Item
            name="difficulty"
            label="难度等级"
            rules={[{ required: true, message: '请选择难度等级' }]}
          >
            <Select placeholder="请选择难度等级">
              <Option value="easy">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="hard">困难</Option>
            </Select>
          </Form.Item>

          <Space>
            <Form.Item name="isFree" valuePropName="checked">
              <Switch checkedChildren="免费" unCheckedChildren="付费" />
            </Form.Item>

            <Form.Item name="isFeatured" valuePropName="checked">
              <Switch checkedChildren="推荐" unCheckedChildren="普通" />
            </Form.Item>

            <Form.Item name="status" valuePropName="checked">
              <Switch checkedChildren="启用" unCheckedChildren="禁用" />
            </Form.Item>
          </Space>
        </Form>
      </Modal>
    </div>
  );
};

export default QuizManagement;
