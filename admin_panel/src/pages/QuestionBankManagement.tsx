import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Card,
  Modal,
  Form,
  message,
  Popconfirm,
  Radio,
  InputNumber
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface Question {
  id: number;
  quizId: number;
  quizTitle: string;
  questionText: string;
  questionType: 'single' | 'multiple' | 'text';
  options: QuestionOption[];
  correctAnswer: string;
  score: number;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  order: number;
  createdAt: string;
}

interface QuestionOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

const QuestionBankManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [form] = Form.useForm();

  // 模拟题目数据
  const [questions, setQuestions] = useState<Question[]>([
    {
      id: 1,
      quizId: 1,
      quizTitle: 'MBTI人格测试',
      questionText: '在社交场合中，你更倾向于：',
      questionType: 'single',
      options: [
        { id: 'A', text: '主动与他人交谈', isCorrect: false },
        { id: 'B', text: '等待他人主动交谈', isCorrect: true },
        { id: 'C', text: '观察周围环境', isCorrect: false },
        { id: 'D', text: '寻找熟悉的人', isCorrect: false }
      ],
      correctAnswer: 'B',
      score: 5,
      difficulty: 'medium',
      category: '人格测试',
      order: 1,
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      quizId: 1,
      quizTitle: 'MBTI人格测试',
      questionText: '做决定时，你更依赖：',
      questionType: 'single',
      options: [
        { id: 'A', text: '逻辑分析', isCorrect: true },
        { id: 'B', text: '直觉感受', isCorrect: false },
        { id: 'C', text: '他人建议', isCorrect: false },
        { id: 'D', text: '过往经验', isCorrect: false }
      ],
      correctAnswer: 'A',
      score: 5,
      difficulty: 'medium',
      category: '人格测试',
      order: 2,
      createdAt: '2024-01-15'
    },
    {
      id: 3,
      quizId: 2,
      quizTitle: '情商测试',
      questionText: '当朋友情绪低落时，你会：',
      questionType: 'single',
      options: [
        { id: 'A', text: '给出建议', isCorrect: false },
        { id: 'B', text: '倾听并陪伴', isCorrect: true },
        { id: 'C', text: '转移话题', isCorrect: false },
        { id: 'D', text: '分享类似经历', isCorrect: false }
      ],
      correctAnswer: 'B',
      score: 10,
      difficulty: 'easy',
      category: '情商测试',
      order: 1,
      createdAt: '2024-01-10'
    }
  ]);

  const columns: ColumnsType<Question> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '所属测评',
      key: 'quiz',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.quizTitle}</div>
          <Tag color="blue">{record.category}</Tag>
        </div>
      )
    },
    {
      title: '题目内容',
      dataIndex: 'questionText',
      key: 'questionText',
      width: 300,
      render: (text: string) => (
        <div style={{ 
          maxWidth: '280px', 
          overflow: 'hidden', 
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          <QuestionCircleOutlined style={{ marginRight: '8px', color: '#E6D4EF' }} />
          {text}
        </div>
      )
    },
    {
      title: '题型',
      dataIndex: 'questionType',
      key: 'questionType',
      width: 80,
      render: (type: string) => {
        const typeMap = { single: '单选', multiple: '多选', text: '文本' };
        const colorMap = { single: 'green', multiple: 'orange', text: 'purple' };
        return <Tag color={colorMap[type as keyof typeof colorMap]}>{typeMap[type as keyof typeof typeMap]}</Tag>;
      }
    },
    {
      title: '难度',
      dataIndex: 'difficulty',
      key: 'difficulty',
      width: 80,
      render: (difficulty: 'easy' | 'medium' | 'hard') => {
        const colorMap: Record<'easy' | 'medium' | 'hard', string> = { easy: 'green', medium: 'orange', hard: 'red' };
        const textMap: Record<'easy' | 'medium' | 'hard', string> = { easy: '简单', medium: '中等', hard: '困难' };
        return <Tag color={colorMap[difficulty]}>{textMap[difficulty]}</Tag>;
      }
    },
    {
      title: '分值',
      dataIndex: 'score',
      key: 'score',
      width: 60,
      render: (score: number) => `${score}分`
    },
    {
      title: '顺序',
      dataIndex: 'order',
      key: 'order',
      width: 60,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这道题目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingQuestion(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (question: Question) => {
    setEditingQuestion(question);
    form.setFieldsValue(question);
    setModalVisible(true);
  };

  const handleView = (question: Question) => {
    Modal.info({
      title: '题目详情',
      width: 700,
      content: (
        <div style={{ marginTop: 16 }}>
          <p><strong>所属测评:</strong> {question.quizTitle}</p>
          <p><strong>题目:</strong> {question.questionText}</p>
          <p><strong>题型:</strong> {question.questionType === 'single' ? '单选题' : question.questionType === 'multiple' ? '多选题' : '文本题'}</p>
          <p><strong>选项:</strong></p>
          <div style={{ marginLeft: 16 }}>
            {question.options.map((option, index) => (
              <div key={option.id} style={{ marginBottom: 8 }}>
                <Tag color={option.isCorrect ? 'green' : 'default'}>
                  {option.id}
                </Tag>
                <span style={{ marginLeft: 8 }}>{option.text}</span>
                {option.isCorrect && <Text type="success" style={{ marginLeft: 8 }}>(正确答案)</Text>}
              </div>
            ))}
          </div>
          <p><strong>分值:</strong> {question.score}分</p>
          <p><strong>难度:</strong> {question.difficulty === 'easy' ? '简单' : question.difficulty === 'medium' ? '中等' : '困难'}</p>
          <p><strong>顺序:</strong> 第{question.order}题</p>
        </div>
      ),
    });
  };

  const handleDelete = (id: number) => {
    setQuestions(questions.filter(q => q.id !== id));
    message.success('题目删除成功');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      if (editingQuestion) {
        // 编辑题目
        setQuestions(questions.map(q => 
          q.id === editingQuestion.id ? { ...q, ...values } : q
        ));
        message.success('题目更新成功');
      } else {
        // 添加题目
        const newQuestion: Question = {
          id: Math.max(...questions.map(q => q.id)) + 1,
          ...values,
          createdAt: new Date().toISOString().split('T')[0]
        };
        setQuestions([...questions, newQuestion]);
        message.success('题目添加成功');
      }
      
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          题库管理
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ backgroundColor: '#E6D4EF', borderColor: '#E6D4EF' }}
        >
          添加题目
        </Button>
      </div>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input
              placeholder="搜索题目内容"
              prefix={<SearchOutlined />}
              style={{ width: 300 }}
            />
            <Select placeholder="所属测评" style={{ width: 150 }}>
              <Option value="">全部测评</Option>
              <Option value="1">MBTI人格测试</Option>
              <Option value="2">情商测试</Option>
              <Option value="3">职业兴趣测试</Option>
            </Select>
            <Select placeholder="题型" style={{ width: 120 }}>
              <Option value="">全部题型</Option>
              <Option value="single">单选题</Option>
              <Option value="multiple">多选题</Option>
              <Option value="text">文本题</Option>
            </Select>
            <Select placeholder="难度" style={{ width: 120 }}>
              <Option value="">全部难度</Option>
              <Option value="easy">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="hard">困难</Option>
            </Select>
            <Button type="primary">搜索</Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={questions}
          rowKey="id"
          loading={loading}
          pagination={{
            total: questions.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 道题目`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 题目编辑模态框 */}
      <Modal
        title={editingQuestion ? '编辑题目' : '添加题目'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        confirmLoading={loading}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            name="quizId"
            label="所属测评"
            rules={[{ required: true, message: '请选择所属测评' }]}
          >
            <Select placeholder="请选择所属测评">
              <Option value={1}>MBTI人格测试</Option>
              <Option value={2}>情商测试</Option>
              <Option value={3}>职业兴趣测试</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="questionText"
            label="题目内容"
            rules={[{ required: true, message: '请输入题目内容' }]}
          >
            <TextArea
              placeholder="请输入题目内容"
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="questionType"
            label="题型"
            rules={[{ required: true, message: '请选择题型' }]}
          >
            <Radio.Group>
              <Radio value="single">单选题</Radio>
              <Radio value="multiple">多选题</Radio>
              <Radio value="text">文本题</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="options"
            label="选项设置"
            rules={[{ required: true, message: '请设置选项' }]}
          >
            <div>
              <div style={{ marginBottom: 8 }}>
                <Input placeholder="选项A" style={{ marginBottom: 8 }} />
                <Input placeholder="选项B" style={{ marginBottom: 8 }} />
                <Input placeholder="选项C" style={{ marginBottom: 8 }} />
                <Input placeholder="选项D" />
              </div>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">正确答案：</Text>
                <Radio.Group>
                  <Radio value="A">A</Radio>
                  <Radio value="B">B</Radio>
                  <Radio value="C">C</Radio>
                  <Radio value="D">D</Radio>
                </Radio.Group>
              </div>
            </div>
          </Form.Item>

          <Form.Item
            name="score"
            label="分值"
            rules={[{ required: true, message: '请输入分值' }]}
          >
            <InputNumber min={1} max={100} placeholder="请输入分值" />
          </Form.Item>

          <Form.Item
            name="difficulty"
            label="难度"
            rules={[{ required: true, message: '请选择难度' }]}
          >
            <Select placeholder="请选择难度">
              <Option value="easy">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="hard">困难</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="order"
            label="题目顺序"
            rules={[{ required: true, message: '请输入题目顺序' }]}
          >
            <InputNumber min={1} placeholder="请输入题目顺序" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default QuestionBankManagement;
