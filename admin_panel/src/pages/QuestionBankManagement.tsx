import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Card,
  Modal,
  Form,
  message,
  Popconfirm,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  BankOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface QuestionBank {
  id: number;
  name: string;
  description: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questionCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

const QuestionBankManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBank, setEditingBank] = useState<QuestionBank | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 模拟题库数据
  const [questionBanks, setQuestionBanks] = useState<QuestionBank[]>([
    {
      id: 1,
      name: 'MBTI人格测试题库',
      description: '包含MBTI人格测试相关的所有题目，用于评估个人性格类型',
      category: '人格测试',
      difficulty: 'medium',
      questionCount: 60,
      status: 'active',
      createdAt: '2024-01-15',
      updatedAt: '2024-01-20'
    },
    {
      id: 2,
      name: '情商测试题库',
      description: '情商评估相关题目，测试情绪智力和社交能力',
      category: '情商测试',
      difficulty: 'easy',
      questionCount: 40,
      status: 'active',
      createdAt: '2024-01-10',
      updatedAt: '2024-01-18'
    },
    {
      id: 3,
      name: '职业兴趣测试题库',
      description: '职业倾向和兴趣评估题目集合',
      category: '职业测试',
      difficulty: 'easy',
      questionCount: 30,
      status: 'active',
      createdAt: '2024-01-08',
      updatedAt: '2024-01-16'
    },
    {
      id: 4,
      name: '心理健康评估题库',
      description: '心理健康状态评估相关题目',
      category: '心理健康',
      difficulty: 'hard',
      questionCount: 25,
      status: 'inactive',
      createdAt: '2024-01-05',
      updatedAt: '2024-01-12'
    }
  ]);

  const columns: ColumnsType<QuestionBank> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '题库信息',
      key: 'bankInfo',
      width: 300,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px', fontSize: '16px' }}>
            <BankOutlined style={{ marginRight: '8px', color: '#E6D4EF' }} />
            {record.name}
          </div>
          <div style={{ color: '#666', fontSize: '12px', marginBottom: '4px' }}>
            {record.description}
          </div>
          <Tag color="blue">{record.category}</Tag>
        </div>
      )
    },
    {
      title: '难度',
      dataIndex: 'difficulty',
      key: 'difficulty',
      width: 80,
      render: (difficulty: 'easy' | 'medium' | 'hard') => {
        const colorMap: Record<'easy' | 'medium' | 'hard', string> = { easy: 'green', medium: 'orange', hard: 'red' };
        const textMap: Record<'easy' | 'medium' | 'hard', string> = { easy: '简单', medium: '中等', hard: '困难' };
        return <Tag color={colorMap[difficulty]}>{textMap[difficulty]}</Tag>;
      }
    },
    {
      title: '题目数量',
      dataIndex: 'questionCount',
      key: 'questionCount',
      width: 100,
      render: (count: number) => (
        <Statistic
          value={count}
          suffix="题"
          valueStyle={{ fontSize: '16px', color: '#E6D4EF' }}
        />
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colorMap = { active: 'green', inactive: 'red' };
        const textMap = { active: '启用', inactive: '禁用' };
        return <Tag color={colorMap[status as keyof typeof colorMap]}>{textMap[status as keyof typeof textMap]}</Tag>;
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<FileTextOutlined />}
            onClick={() => handleManageQuestions(record)}
            style={{ backgroundColor: '#E6D4EF', borderColor: '#E6D4EF' }}
          >
            管理题目
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个题库吗？删除后题库中的所有题目也会被删除！"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingBank(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (bank: QuestionBank) => {
    setEditingBank(bank);
    form.setFieldsValue(bank);
    setModalVisible(true);
  };

  const handleManageQuestions = (bank: QuestionBank) => {
    // 导航到题目管理页面，传递题库ID
    navigate(`/question-bank-management/${bank.id}/questions`, {
      state: { bankName: bank.name }
    });
  };

  const handleDelete = (id: number) => {
    setQuestionBanks(questionBanks.filter(bank => bank.id !== id));
    message.success('题库删除成功，相关题目也已删除');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingBank) {
        // 编辑题库
        setQuestionBanks(questionBanks.map(bank =>
          bank.id === editingBank.id
            ? { ...bank, ...values, updatedAt: new Date().toISOString().split('T')[0] }
            : bank
        ));
        message.success('题库更新成功');
      } else {
        // 添加题库
        const newBank: QuestionBank = {
          id: Math.max(...questionBanks.map(bank => bank.id)) + 1,
          ...values,
          questionCount: 0,
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0]
        };
        setQuestionBanks([...questionBanks, newBank]);
        message.success('题库添加成功');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          题库管理
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ backgroundColor: '#E6D4EF', borderColor: '#E6D4EF' }}
        >
          添加题库
        </Button>
      </div>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input
              placeholder="搜索题库名称或描述"
              prefix={<SearchOutlined />}
              style={{ width: 300 }}
            />
            <Select placeholder="分类" style={{ width: 150 }}>
              <Option value="">全部分类</Option>
              <Option value="人格测试">人格测试</Option>
              <Option value="情商测试">情商测试</Option>
              <Option value="职业测试">职业测试</Option>
              <Option value="心理健康">心理健康</Option>
            </Select>
            <Select placeholder="难度" style={{ width: 120 }}>
              <Option value="">全部难度</Option>
              <Option value="easy">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="hard">困难</Option>
            </Select>
            <Select placeholder="状态" style={{ width: 120 }}>
              <Option value="">全部状态</Option>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
            <Button type="primary">搜索</Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={questionBanks}
          rowKey="id"
          loading={loading}
          pagination={{
            total: questionBanks.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个题库`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 题库编辑模态框 */}
      <Modal
        title={editingBank ? '编辑题库' : '添加题库'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            name="name"
            label="题库名称"
            rules={[{ required: true, message: '请输入题库名称' }]}
          >
            <Input placeholder="请输入题库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="题库描述"
            rules={[{ required: true, message: '请输入题库描述' }]}
          >
            <TextArea
              placeholder="请输入题库描述"
              rows={3}
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="题库分类"
            rules={[{ required: true, message: '请选择题库分类' }]}
          >
            <Select placeholder="请选择题库分类">
              <Option value="人格测试">人格测试</Option>
              <Option value="情商测试">情商测试</Option>
              <Option value="职业测试">职业测试</Option>
              <Option value="心理健康">心理健康</Option>
              <Option value="智力测试">智力测试</Option>
              <Option value="兴趣爱好">兴趣爱好</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="difficulty"
            label="题库难度"
            rules={[{ required: true, message: '请选择题库难度' }]}
          >
            <Select placeholder="请选择题库难度">
              <Option value="easy">简单</Option>
              <Option value="medium">中等</Option>
              <Option value="hard">困难</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="题库状态"
            rules={[{ required: true, message: '请选择题库状态' }]}
          >
            <Select placeholder="请选择题库状态">
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default QuestionBankManagement;
