import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Card,
  Modal,
  message,
  Popconfirm,
  Avatar,
  DatePicker,
  Progress,
  Descriptions
} from 'antd';
import {
  EyeOutlined,
  DeleteOutlined,
  SearchOutlined,
  UserOutlined,
  FileTextOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface QuizResult {
  id: number;
  userId: number;
  username: string;
  nickname: string;
  quizId: number;
  quizTitle: string;
  quizCategory: string;
  score: number;
  maxScore: number;
  percentage: number;
  result: string;
  resultType: string;
  completedAt: string;
  duration: number; // 完成时间（分钟）
  answers: QuizAnswer[];
}

interface QuizAnswer {
  questionId: number;
  questionText: string;
  selectedOption: string;
  isCorrect: boolean;
  score: number;
}

const QuizResultManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedResult, setSelectedResult] = useState<QuizResult | null>(null);

  // 模拟测评结果数据
  const [quizResults, setQuizResults] = useState<QuizResult[]>([
    {
      id: 1,
      userId: 1,
      username: 'zhangsan',
      nickname: '张三',
      quizId: 1,
      quizTitle: 'MBTI人格测试',
      quizCategory: '人格测试',
      score: 85,
      maxScore: 100,
      percentage: 85,
      result: 'ENFP - 竞选者',
      resultType: 'ENFP',
      completedAt: '2024-01-20 14:30:00',
      duration: 12,
      answers: [
        {
          questionId: 1,
          questionText: '你更喜欢与人交往还是独处？',
          selectedOption: 'A. 与人交往',
          isCorrect: true,
          score: 5
        },
        {
          questionId: 2,
          questionText: '你更注重细节还是大局？',
          selectedOption: 'B. 大局',
          isCorrect: true,
          score: 5
        }
      ]
    },
    {
      id: 2,
      userId: 2,
      username: 'lisi',
      nickname: '李四',
      quizId: 2,
      quizTitle: '情商测试',
      quizCategory: '情商测试',
      score: 92,
      maxScore: 100,
      percentage: 92,
      result: '高情商',
      resultType: 'HIGH_EQ',
      completedAt: '2024-01-19 16:45:00',
      duration: 8,
      answers: []
    },
    {
      id: 3,
      userId: 3,
      username: 'wangwu',
      nickname: '王五',
      quizId: 3,
      quizTitle: '职业兴趣测试',
      quizCategory: '职业测试',
      score: 78,
      maxScore: 100,
      percentage: 78,
      result: '艺术型',
      resultType: 'ARTISTIC',
      completedAt: '2024-01-18 10:20:00',
      duration: 15,
      answers: []
    }
  ]);

  const columns: ColumnsType<QuizResult> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '用户信息',
      key: 'userInfo',
      width: 150,
      render: (_, record) => (
        <Space>
          <Avatar 
            icon={<UserOutlined />}
            style={{ backgroundColor: '#E6D4EF' }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>@{record.username}</div>
          </div>
        </Space>
      )
    },
    {
      title: '测评信息',
      key: 'quizInfo',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
            <FileTextOutlined style={{ marginRight: '4px', color: '#E6D4EF' }} />
            {record.quizTitle}
          </div>
          <Tag color="blue">{record.quizCategory}</Tag>
        </div>
      )
    },
    {
      title: '得分',
      key: 'score',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
            {record.score}/{record.maxScore}
          </div>
          <Progress 
            percent={record.percentage} 
            size="small" 
            strokeColor="#E6D4EF"
            showInfo={false}
          />
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.percentage}%
          </div>
        </div>
      )
    },
    {
      title: '测评结果',
      key: 'result',
      width: 150,
      render: (_, record) => (
        <div>
          <Tag color="purple" style={{ marginBottom: '4px' }}>
            {record.resultType}
          </Tag>
          <div style={{ fontSize: '12px' }}>{record.result}</div>
        </div>
      )
    },
    {
      title: '完成时间',
      dataIndex: 'completedAt',
      key: 'completedAt',
      width: 120,
      render: (date: string) => (
        <div>
          <div>{dayjs(date).format('YYYY-MM-DD')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(date).format('HH:mm:ss')}
          </div>
        </div>
      )
    },
    {
      title: '用时',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      render: (duration: number) => `${duration}分钟`
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>
          <Popconfirm
            title="确定要删除这条测评记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (result: QuizResult) => {
    setSelectedResult(result);
    setDetailModalVisible(true);
  };

  const handleDelete = (id: number) => {
    setQuizResults(quizResults.filter(result => result.id !== id));
    message.success('测评记录删除成功');
  };

  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          用户测评答案管理
        </Title>
        <Button
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleExport}
          style={{ backgroundColor: '#E6D4EF', borderColor: '#E6D4EF' }}
        >
          导出数据
        </Button>
      </div>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="搜索用户名或测评名称"
              prefix={<SearchOutlined />}
              style={{ width: 250 }}
            />
            <Select placeholder="测评分类" style={{ width: 150 }}>
              <Option value="">全部分类</Option>
              <Option value="人格测试">人格测试</Option>
              <Option value="情商测试">情商测试</Option>
              <Option value="职业测试">职业测试</Option>
              <Option value="恋爱测试">恋爱测试</Option>
            </Select>
            <Select placeholder="得分范围" style={{ width: 120 }}>
              <Option value="">全部</Option>
              <Option value="90-100">90-100分</Option>
              <Option value="80-89">80-89分</Option>
              <Option value="70-79">70-79分</Option>
              <Option value="60-69">60-69分</Option>
              <Option value="0-59">60分以下</Option>
            </Select>
            <RangePicker placeholder={['开始日期', '结束日期']} />
            <Button type="primary">搜索</Button>
            <Button>重置</Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={quizResults}
          rowKey="id"
          loading={loading}
          pagination={{
            total: quizResults.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="测评详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedResult && (
          <div>
            <Descriptions title="基本信息" bordered column={2} style={{ marginBottom: 24 }}>
              <Descriptions.Item label="用户">{selectedResult.nickname} (@{selectedResult.username})</Descriptions.Item>
              <Descriptions.Item label="测评名称">{selectedResult.quizTitle}</Descriptions.Item>
              <Descriptions.Item label="测评分类">{selectedResult.quizCategory}</Descriptions.Item>
              <Descriptions.Item label="完成时间">{selectedResult.completedAt}</Descriptions.Item>
              <Descriptions.Item label="用时">{selectedResult.duration}分钟</Descriptions.Item>
              <Descriptions.Item label="得分">
                <Space>
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {selectedResult.score}/{selectedResult.maxScore}
                  </span>
                  <Tag color="purple">{selectedResult.percentage}%</Tag>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="测评结果" span={2}>
                <Space>
                  <Tag color="purple">{selectedResult.resultType}</Tag>
                  <span>{selectedResult.result}</span>
                </Space>
              </Descriptions.Item>
            </Descriptions>

            {selectedResult.answers.length > 0 && (
              <div>
                <Title level={4}>答题详情</Title>
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {selectedResult.answers.map((answer, index) => (
                    <Card
                      key={answer.questionId}
                      size="small"
                      style={{ marginBottom: 8 }}
                      title={`第${index + 1}题`}
                    >
                      <p><strong>题目：</strong>{answer.questionText}</p>
                      <p>
                        <strong>选择：</strong>
                        <Tag color={answer.isCorrect ? 'green' : 'red'} style={{ marginLeft: 8 }}>
                          {answer.selectedOption}
                        </Tag>
                      </p>
                      <p><strong>得分：</strong>{answer.score}分</p>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default QuizResultManagement;
