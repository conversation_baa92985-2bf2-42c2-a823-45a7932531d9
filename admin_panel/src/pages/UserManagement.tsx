import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  Card,
  Modal,
  Form,
  message,
  Popconfirm,
  Avatar,
  DatePicker,
  Switch
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  UserOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

interface User {
  id: number;
  username: string;
  email: string;
  phone: string;
  nickname: string;
  avatar?: string;
  gender: 'male' | 'female' | 'other';
  age: number;
  vipLevel: 'normal' | 'vip' | 'svip';
  status: 'active' | 'inactive' | 'banned';
  registeredAt: string;
  lastLoginAt: string;
  quizCount: number;
  totalScore: number;
}

const UserManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  // 模拟用户数据
  const [users, setUsers] = useState<User[]>([
    {
      id: 1,
      username: 'zhangsan',
      email: '<EMAIL>',
      phone: '13800138001',
      nickname: '张三',
      gender: 'male',
      age: 25,
      vipLevel: 'vip',
      status: 'active',
      registeredAt: '2024-01-15',
      lastLoginAt: '2024-01-20',
      quizCount: 15,
      totalScore: 1250
    },
    {
      id: 2,
      username: 'lisi',
      email: '<EMAIL>',
      phone: '13800138002',
      nickname: '李四',
      gender: 'female',
      age: 28,
      vipLevel: 'svip',
      status: 'active',
      registeredAt: '2024-01-10',
      lastLoginAt: '2024-01-19',
      quizCount: 32,
      totalScore: 2890
    },
    {
      id: 3,
      username: 'wangwu',
      email: '<EMAIL>',
      phone: '13800138003',
      nickname: '王五',
      gender: 'male',
      age: 22,
      vipLevel: 'normal',
      status: 'inactive',
      registeredAt: '2024-01-08',
      lastLoginAt: '2024-01-18',
      quizCount: 8,
      totalScore: 650
    }
  ]);

  const columns: ColumnsType<User> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.avatar}
            icon={<UserOutlined />}
            style={{ backgroundColor: '#E6D4EF' }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>@{record.username}</div>
          </div>
        </Space>
      )
    },
    {
      title: '联系方式',
      key: 'contact',
      width: 180,
      render: (_, record) => (
        <div>
          <div>{record.email}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.phone}</div>
        </div>
      )
    },
    {
      title: '性别/年龄',
      key: 'profile',
      width: 100,
      render: (_, record) => (
        <div>
          <Tag color={record.gender === 'male' ? 'blue' : record.gender === 'female' ? 'pink' : 'default'}>
            {record.gender === 'male' ? '男' : record.gender === 'female' ? '女' : '其他'}
          </Tag>
          <div style={{ fontSize: '12px', marginTop: '4px' }}>{record.age}岁</div>
        </div>
      )
    },
    {
      title: 'VIP等级',
      dataIndex: 'vipLevel',
      key: 'vipLevel',
      width: 100,
      render: (vipLevel: string) => {
        const colorMap = { normal: 'default', vip: 'gold', svip: 'purple' };
        const textMap = { normal: '普通', vip: 'VIP', svip: 'SVIP' };
        return <Tag color={colorMap[vipLevel as keyof typeof colorMap]}>{textMap[vipLevel as keyof typeof textMap]}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const colorMap = { active: 'green', inactive: 'orange', banned: 'red' };
        const textMap = { active: '正常', inactive: '未激活', banned: '已封禁' };
        return <Tag color={colorMap[status as keyof typeof colorMap]}>{textMap[status as keyof typeof textMap]}</Tag>;
      }
    },
    {
      title: '测评数据',
      key: 'quizData',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.quizCount} 次测评</div>
          <div style={{ fontSize: '12px', color: '#666' }}>总分: {record.totalScore}</div>
        </div>
      )
    },
    {
      title: '注册时间',
      dataIndex: 'registeredAt',
      key: 'registeredAt',
      width: 100,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 100,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue({
      ...user,
      registeredAt: dayjs(user.registeredAt),
    });
    setModalVisible(true);
  };

  const handleView = (user: User) => {
    Modal.info({
      title: '用户详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <p><strong>用户名:</strong> {user.username}</p>
          <p><strong>昵称:</strong> {user.nickname}</p>
          <p><strong>邮箱:</strong> {user.email}</p>
          <p><strong>手机:</strong> {user.phone}</p>
          <p><strong>性别:</strong> {user.gender === 'male' ? '男' : user.gender === 'female' ? '女' : '其他'}</p>
          <p><strong>年龄:</strong> {user.age}岁</p>
          <p><strong>VIP等级:</strong> {user.vipLevel === 'normal' ? '普通' : user.vipLevel === 'vip' ? 'VIP' : 'SVIP'}</p>
          <p><strong>状态:</strong> {user.status === 'active' ? '正常' : user.status === 'inactive' ? '未激活' : '已封禁'}</p>
          <p><strong>测评次数:</strong> {user.quizCount}</p>
          <p><strong>总分:</strong> {user.totalScore}</p>
          <p><strong>注册时间:</strong> {dayjs(user.registeredAt).format('YYYY-MM-DD HH:mm:ss')}</p>
          <p><strong>最后登录:</strong> {dayjs(user.lastLoginAt).format('YYYY-MM-DD HH:mm:ss')}</p>
        </div>
      ),
    });
  };

  const handleDelete = (id: number) => {
    setUsers(users.filter(user => user.id !== id));
    message.success('用户删除成功');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingUser) {
        // 编辑用户
        setUsers(users.map(user =>
          user.id === editingUser.id
            ? { ...user, ...values, registeredAt: values.registeredAt.format('YYYY-MM-DD') }
            : user
        ));
        message.success('用户更新成功');
      } else {
        // 添加用户
        const newUser: User = {
          id: Math.max(...users.map(u => u.id)) + 1,
          ...values,
          registeredAt: values.registeredAt.format('YYYY-MM-DD'),
          lastLoginAt: dayjs().format('YYYY-MM-DD'),
          quizCount: 0,
          totalScore: 0
        };
        setUsers([...users, newUser]);
        message.success('用户添加成功');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          用户管理
        </Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          style={{ backgroundColor: '#E6D4EF', borderColor: '#E6D4EF' }}
        >
          添加用户
        </Button>
      </div>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input
              placeholder="搜索用户名、昵称或邮箱"
              prefix={<SearchOutlined />}
              style={{ width: 300 }}
            />
            <Select placeholder="VIP等级" style={{ width: 120 }}>
              <Option value="">全部</Option>
              <Option value="normal">普通</Option>
              <Option value="vip">VIP</Option>
              <Option value="svip">SVIP</Option>
            </Select>
            <Select placeholder="状态" style={{ width: 120 }}>
              <Option value="">全部</Option>
              <Option value="active">正常</Option>
              <Option value="inactive">未激活</Option>
              <Option value="banned">已封禁</Option>
            </Select>
            <Button type="primary">搜索</Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            total: users.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
            rules={[{ required: true, message: '请输入昵称' }]}
          >
            <Input placeholder="请输入昵称" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[{ required: true, message: '请输入手机号' }]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item
            name="gender"
            label="性别"
            rules={[{ required: true, message: '请选择性别' }]}
          >
            <Select placeholder="请选择性别">
              <Option value="male">男</Option>
              <Option value="female">女</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="age"
            label="年龄"
            rules={[{ required: true, message: '请输入年龄' }]}
          >
            <Input type="number" placeholder="请输入年龄" />
          </Form.Item>

          <Form.Item
            name="vipLevel"
            label="VIP等级"
            rules={[{ required: true, message: '请选择VIP等级' }]}
          >
            <Select placeholder="请选择VIP等级">
              <Option value="normal">普通</Option>
              <Option value="vip">VIP</Option>
              <Option value="svip">SVIP</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="active">正常</Option>
              <Option value="inactive">未激活</Option>
              <Option value="banned">已封禁</Option>
            </Select>
          </Form.Item>

          {!editingUser && (
            <Form.Item
              name="registeredAt"
              label="注册时间"
              rules={[{ required: true, message: '请选择注册时间' }]}
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
