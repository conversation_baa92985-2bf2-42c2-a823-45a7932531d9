import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  User? _user;
  String? _token;
  bool _isLoading = false;
  String? _error;

  // Getters
  User? get user => _user;
  String? get token => _token;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null && _token != null;

  AuthProvider() {
    _initializeAuth();
  }

  /// 初始化认证状态
  Future<void> _initializeAuth() async {
    await _loadUserFromStorage();
    // 测试模式：如果没有登录用户，自动登录测试用户
    await autoLoginForTesting();
  }

  /// 测试模式自动登录
  Future<void> autoLoginForTesting() async {
    if (!isLoggedIn) {
      // 使用测试用户信息
      _token =
          "eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************.YZdmtAWUYNPD-WYKkovTYdDiUmEi2SMAyur7tekc6Nc";
      _user = User(
        id: 3,
        username: "testuser2024",
        email: "<EMAIL>",
        nickname: "测试用户2024",
        status: 1,
        isVip: false,
        createdAt: DateTime.now(),
      );
      await _saveUserToStorage();
      notifyListeners();
      debugPrint('测试模式：自动登录成功');
    }
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      final userJson = prefs.getString('user_data');

      if (token != null && userJson != null) {
        _token = token;
        _user = User.fromJson(userJson);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('加载用户信息失败: $e');
    }
  }

  /// 保存用户信息到本地存储
  Future<void> _saveUserToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_token != null && _user != null) {
        await prefs.setString('auth_token', _token!);
        await prefs.setString('user_data', _user!.toJson());
      }
    } catch (e) {
      debugPrint('保存用户信息失败: $e');
    }
  }

  /// 清除本地存储的用户信息
  Future<void> _clearUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('user_data');
    } catch (e) {
      debugPrint('清除用户信息失败: $e');
    }
  }

  /// 用户登录
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.login(username, password);

      if (result['success'] == true) {
        _token = result['token'];
        _user = User.fromMap(result['user']);
        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '登录失败');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('登录失败: $e');
      _setLoading(false);
      return false;
    }
  }

  /// 用户注册
  Future<bool> register(String username, String email, String password,
      {String? nickname}) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.register(username, email, password,
          nickname: nickname);

      if (result['success'] == true) {
        _token = result['token'];
        _user = User.fromMap(result['user']);
        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '注册失败');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('注册失败: $e');
      _setLoading(false);
      return false;
    }
  }

  /// 用户登出
  Future<void> logout() async {
    _user = null;
    _token = null;
    await _clearUserFromStorage();
    notifyListeners();
  }

  /// 更新用户信息
  Future<bool> updateProfile(Map<String, dynamic> userData) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.updateProfile(userData);

      if (result['success'] == true) {
        _user = User.fromMap(result['user']);
        await _saveUserToStorage();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '更新失败');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('更新失败: $e');
      _setLoading(false);
      return false;
    }
  }

  /// 刷新Token
  Future<bool> refreshToken() async {
    if (_token == null) return false;

    try {
      final result = await _authService.refreshToken(_token!);

      if (result['success'] == true) {
        _token = result['token'];
        await _saveUserToStorage();
        notifyListeners();
        return true;
      } else {
        // Token刷新失败，需要重新登录
        await logout();
        return false;
      }
    } catch (e) {
      debugPrint('Token刷新失败: $e');
      await logout();
      return false;
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
