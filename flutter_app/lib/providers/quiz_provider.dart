import 'package:flutter/foundation.dart';
import '../models/quiz.dart';
import '../models/question.dart';
import '../models/quiz_result.dart';
import '../services/quiz_service.dart';

class QuizProvider with ChangeNotifier {
  final QuizService _quizService = QuizService();

  // 测评列表相关
  List<Quiz> _quizzes = [];
  List<Quiz> _featuredQuizzes = [];
  bool _isLoadingQuizzes = false;

  // 当前测评相关
  Quiz? _currentQuiz;
  List<Question> _questions = [];
  Map<int, dynamic> _answers = {};
  int _currentQuestionIndex = 0;
  bool _isLoadingQuiz = false;

  // 测评结果相关
  QuizResult? _currentResult;
  List<QuizResult> _userResults = [];
  bool _isLoadingResult = false;

  String? _error;

  // Getters
  List<Quiz> get quizzes => _quizzes;
  List<Quiz> get featuredQuizzes => _featuredQuizzes;
  bool get isLoadingQuizzes => _isLoadingQuizzes;

  Quiz? get currentQuiz => _currentQuiz;
  List<Question> get questions => _questions;
  Map<int, dynamic> get answers => _answers;
  int get currentQuestionIndex => _currentQuestionIndex;
  bool get isLoadingQuiz => _isLoadingQuiz;

  QuizResult? get currentResult => _currentResult;
  List<QuizResult> get userResults => _userResults;
  bool get isLoadingResult => _isLoadingResult;

  String? get error => _error;

  // 计算属性
  bool get hasNextQuestion => _currentQuestionIndex < _questions.length - 1;
  bool get hasPreviousQuestion => _currentQuestionIndex > 0;
  double get progress => _questions.isEmpty
      ? 0.0
      : (_currentQuestionIndex + 1) / _questions.length;
  Question? get currentQuestion =>
      _questions.isEmpty ? null : _questions[_currentQuestionIndex];

  /// 获取测评列表
  Future<void> fetchQuizzes({String? category}) async {
    _isLoadingQuizzes = true;
    _clearError();
    notifyListeners();

    try {
      // 先尝试从API获取数据
      try {
        final result = await _quizService.getQuizzes(category: category);

        if (result['success'] == true) {
          _quizzes = (result['data'] as List)
              .map((json) => Quiz.fromMap(json))
              .toList();
          _isLoadingQuizzes = false;
          notifyListeners();
          return;
        }
      } catch (e) {
        debugPrint('API调用失败，使用模拟数据: $e');
      }

      // 如果API失败，使用模拟数据
      _quizzes = _getMockQuizzes(category: category);
    } catch (e) {
      _setError('获取测评列表失败: $e');
    } finally {
      _isLoadingQuizzes = false;
      notifyListeners();
    }
  }

  /// 获取模拟测评数据
  List<Quiz> _getMockQuizzes({String? category}) {
    final allQuizzes = [
      Quiz(
        id: 1,
        title: 'MBTI人格测试',
        subtitle: '16型人格测评',
        description: '通过科学的心理学测评，了解你的人格类型和行为偏好。',
        categoryName: '人格测试',
        questionCount: 60,
        estimatedTime: 15,
        difficultyLevel: 2,
        isFree: true,
        price: 0.0,
        isFeatured: true,
        viewCount: 1000,
        takeCount: 500,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Quiz(
        id: 2,
        title: '情商测试',
        subtitle: 'EQ情绪智力测评',
        description: '评估你的情绪智力水平，了解自己在情绪管理、人际交往等方面的能力。',
        categoryName: '情商测试',
        questionCount: 40,
        estimatedTime: 10,
        difficultyLevel: 1,
        isFree: true,
        price: 0.0,
        isFeatured: true,
        viewCount: 800,
        takeCount: 400,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Quiz(
        id: 3,
        title: '职业兴趣测试',
        subtitle: '霍兰德职业测评',
        description: '基于霍兰德职业兴趣理论，帮助你发现适合的职业方向和工作环境。',
        categoryName: '职业测试',
        questionCount: 50,
        estimatedTime: 12,
        difficultyLevel: 2,
        isFree: true,
        price: 0.0,
        isFeatured: false,
        viewCount: 600,
        takeCount: 300,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Quiz(
        id: 4,
        title: '恋爱人格测试',
        subtitle: '恋爱观测评',
        description: '了解你在恋爱关系中的行为模式和偏好，帮助你建立更健康的亲密关系。',
        categoryName: '恋爱测试',
        questionCount: 30,
        estimatedTime: 8,
        difficultyLevel: 1,
        isFree: true,
        price: 0.0,
        isFeatured: false,
        viewCount: 400,
        takeCount: 200,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    if (category != null) {
      return allQuizzes.where((quiz) => quiz.categoryName == category).toList();
    }
    return allQuizzes;
  }

  /// 获取推荐测评
  Future<void> fetchFeaturedQuizzes() async {
    try {
      // 先尝试从API获取数据
      try {
        final result = await _quizService.getFeaturedQuizzes();

        if (result['success'] == true) {
          _featuredQuizzes = (result['data'] as List)
              .map((json) => Quiz.fromMap(json))
              .toList();
          notifyListeners();
          return;
        }
      } catch (e) {
        debugPrint('API调用失败，使用模拟数据: $e');
      }

      // 如果API失败，使用模拟数据
      _featuredQuizzes =
          _getMockQuizzes().where((quiz) => quiz.isFeatured).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('获取推荐测评失败: $e');
    }
  }

  /// 获取测评详情
  Future<bool> fetchQuizDetail(String quizId) async {
    _isLoadingQuiz = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizDetail(quizId);

      if (result['success'] == true) {
        _currentQuiz = Quiz.fromMap(result['data']);
        _isLoadingQuiz = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取测评详情失败');
        _isLoadingQuiz = false;
        return false;
      }
    } catch (e) {
      _setError('获取测评详情失败: $e');
      _isLoadingQuiz = false;
      return false;
    }
  }

  /// 开始测评，获取题目
  Future<bool> startQuiz(String quizId) async {
    _isLoadingQuiz = true;
    _clearError();
    _resetQuizState();
    notifyListeners();

    try {
      final result = await _quizService.getQuizQuestions(quizId);

      if (result['success'] == true) {
        _questions = (result['data'] as List)
            .map((json) => Question.fromMap(json))
            .toList();
        _currentQuestionIndex = 0;
        _isLoadingQuiz = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取题目失败');
        _isLoadingQuiz = false;
        return false;
      }
    } catch (e) {
      _setError('获取题目失败: $e');
      _isLoadingQuiz = false;
      return false;
    }
  }

  /// 回答问题
  void answerQuestion(int questionId, dynamic answer) {
    _answers[questionId] = answer;
    notifyListeners();
  }

  /// 下一题
  void nextQuestion() {
    if (hasNextQuestion) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  /// 上一题
  void previousQuestion() {
    if (hasPreviousQuestion) {
      _currentQuestionIndex--;
      notifyListeners();
    }
  }

  /// 跳转到指定题目
  void goToQuestion(int index) {
    if (index >= 0 && index < _questions.length) {
      _currentQuestionIndex = index;
      notifyListeners();
    }
  }

  /// 提交测评
  Future<bool> submitQuiz(String quizId) async {
    _isLoadingResult = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.submitQuiz(quizId, _answers);

      if (result['success'] == true) {
        _currentResult = QuizResult.fromMap(result['data']);
        _isLoadingResult = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '提交测评失败');
        _isLoadingResult = false;
        return false;
      }
    } catch (e) {
      _setError('提交测评失败: $e');
      _isLoadingResult = false;
      return false;
    }
  }

  /// 获取测评结果
  Future<bool> fetchQuizResult(String recordId) async {
    _isLoadingResult = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizResult(recordId);

      if (result['success'] == true) {
        _currentResult = QuizResult.fromMap(result['data']);
        _isLoadingResult = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取结果失败');
        _isLoadingResult = false;
        return false;
      }
    } catch (e) {
      _setError('获取结果失败: $e');
      _isLoadingResult = false;
      return false;
    }
  }

  /// 获取用户历史结果
  Future<void> fetchUserResults() async {
    try {
      final result = await _quizService.getUserResults();

      if (result['success'] == true) {
        _userResults = (result['data'] as List)
            .map((json) => QuizResult.fromMap(json))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('获取历史结果失败: $e');
    }
  }

  /// 重置测评状态
  void _resetQuizState() {
    _questions.clear();
    _answers.clear();
    _currentQuestionIndex = 0;
    _currentResult = null;
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
  }

  /// 清除当前测评
  void clearCurrentQuiz() {
    _currentQuiz = null;
    _resetQuizState();
    notifyListeners();
  }
}
