import 'package:flutter/foundation.dart';
import '../models/quiz.dart';
import '../models/question.dart';
import '../models/quiz_result.dart';
import '../services/quiz_service.dart';

class QuizProvider with ChangeNotifier {
  final QuizService _quizService = QuizService();

  // 测评列表相关
  List<Quiz> _quizzes = [];
  List<Quiz> _featuredQuizzes = [];
  bool _isLoadingQuizzes = false;

  // 当前测评相关
  Quiz? _currentQuiz;
  List<Question> _questions = [];
  Map<int, dynamic> _answers = {};
  int _currentQuestionIndex = 0;
  bool _isLoadingQuiz = false;

  // 测评结果相关
  QuizResult? _currentResult;
  List<QuizResult> _userResults = [];
  bool _isLoadingResult = false;

  String? _error;

  // Getters
  List<Quiz> get quizzes => _quizzes;
  List<Quiz> get featuredQuizzes => _featuredQuizzes;
  bool get isLoadingQuizzes => _isLoadingQuizzes;

  Quiz? get currentQuiz => _currentQuiz;
  List<Question> get questions => _questions;
  Map<int, dynamic> get answers => _answers;
  int get currentQuestionIndex => _currentQuestionIndex;
  bool get isLoadingQuiz => _isLoadingQuiz;

  QuizResult? get currentResult => _currentResult;
  List<QuizResult> get userResults => _userResults;
  bool get isLoadingResult => _isLoadingResult;

  String? get error => _error;

  // 计算属性
  bool get hasNextQuestion => _currentQuestionIndex < _questions.length - 1;
  bool get hasPreviousQuestion => _currentQuestionIndex > 0;
  double get progress => _questions.isEmpty
      ? 0.0
      : (_currentQuestionIndex + 1) / _questions.length;
  Question? get currentQuestion =>
      _questions.isEmpty ? null : _questions[_currentQuestionIndex];

  /// 获取测评列表
  Future<void> fetchQuizzes({String? category}) async {
    _isLoadingQuizzes = true;
    _clearError();
    notifyListeners();

    try {
      // 先尝试从API获取数据
      try {
        final result = await _quizService.getQuizzes(category: category);

        if (result['success'] == true && result['data'] != null) {
          final data = result['data'];
          List<dynamic> quizList;

          // 处理不同的数据结构
          if (data is List) {
            quizList = data;
          } else if (data is Map && data['list'] is List) {
            quizList = data['list'];
          } else {
            throw Exception('Unexpected data format');
          }

          _quizzes = quizList.map((json) => Quiz.fromMap(json)).toList();
          _isLoadingQuizzes = false;
          notifyListeners();
          return;
        }
      } catch (e) {
        debugPrint('API调用失败，使用模拟数据: $e');
      }

      // 如果API失败，使用模拟数据
      _quizzes = _getMockQuizzes(category: category);
    } catch (e) {
      _setError('获取测评列表失败: $e');
    } finally {
      _isLoadingQuizzes = false;
      notifyListeners();
    }
  }

  /// 获取模拟测评数据
  List<Quiz> _getMockQuizzes({String? category}) {
    final allQuizzes = [
      Quiz(
        id: 1,
        title: 'MBTI人格测试',
        subtitle: '16型人格测评',
        description: '通过科学的心理学测评，了解你的人格类型和行为偏好。',
        categoryName: '人格测试',
        questionCount: 60,
        estimatedTime: 15,
        difficultyLevel: 2,
        isFree: true,
        price: 0.0,
        isFeatured: true,
        viewCount: 1000,
        takeCount: 500,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Quiz(
        id: 2,
        title: '情商测试',
        subtitle: 'EQ情绪智力测评',
        description: '评估你的情绪智力水平，了解自己在情绪管理、人际交往等方面的能力。',
        categoryName: '情商测试',
        questionCount: 40,
        estimatedTime: 10,
        difficultyLevel: 1,
        isFree: true,
        price: 0.0,
        isFeatured: true,
        viewCount: 800,
        takeCount: 400,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Quiz(
        id: 3,
        title: '职业兴趣测试',
        subtitle: '霍兰德职业测评',
        description: '基于霍兰德职业兴趣理论，帮助你发现适合的职业方向和工作环境。',
        categoryName: '职业测试',
        questionCount: 50,
        estimatedTime: 12,
        difficultyLevel: 2,
        isFree: true,
        price: 0.0,
        isFeatured: false,
        viewCount: 600,
        takeCount: 300,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Quiz(
        id: 4,
        title: '恋爱人格测试',
        subtitle: '恋爱观测评',
        description: '了解你在恋爱关系中的行为模式和偏好，帮助你建立更健康的亲密关系。',
        categoryName: '恋爱测试',
        questionCount: 30,
        estimatedTime: 8,
        difficultyLevel: 1,
        isFree: true,
        price: 0.0,
        isFeatured: false,
        viewCount: 400,
        takeCount: 200,
        status: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    if (category != null) {
      return allQuizzes.where((quiz) => quiz.categoryName == category).toList();
    }
    return allQuizzes;
  }

  /// 获取推荐测评
  Future<void> fetchFeaturedQuizzes() async {
    try {
      // 先尝试从API获取数据
      try {
        final result = await _quizService.getFeaturedQuizzes();

        if (result['success'] == true && result['data'] != null) {
          final data = result['data'];
          List<dynamic> quizList;

          // 处理不同的数据结构
          if (data is List) {
            quizList = data;
          } else if (data is Map && data['list'] is List) {
            quizList = data['list'];
          } else {
            throw Exception('Unexpected data format');
          }

          _featuredQuizzes =
              quizList.map((json) => Quiz.fromMap(json)).toList();
          notifyListeners();
          return;
        }
      } catch (e) {
        debugPrint('API调用失败，使用模拟数据: $e');
      }

      // 如果API失败，使用模拟数据
      _featuredQuizzes =
          _getMockQuizzes().where((quiz) => quiz.isFeatured).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('获取推荐测评失败: $e');
    }
  }

  /// 获取测评详情
  Future<bool> fetchQuizDetail(String quizId) async {
    _isLoadingQuiz = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizDetail(quizId);

      if (result['success'] == true) {
        _currentQuiz = Quiz.fromMap(result['data']);
        _isLoadingQuiz = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取测评详情失败');
        _isLoadingQuiz = false;
        return false;
      }
    } catch (e) {
      _setError('获取测评详情失败: $e');
      _isLoadingQuiz = false;
      return false;
    }
  }

  /// 开始测评，获取题目
  Future<bool> startQuiz(String quizId) async {
    _isLoadingQuiz = true;
    _clearError();
    _resetQuizState();
    notifyListeners();

    try {
      // 先尝试从API获取数据
      try {
        final result = await _quizService.getQuizQuestions(quizId);

        if (result['success'] == true) {
          _questions = (result['data'] as List)
              .map((json) => Question.fromMap(json))
              .toList();
          _currentQuestionIndex = 0;
          _isLoadingQuiz = false;
          notifyListeners();
          return true;
        }
      } catch (e) {
        debugPrint('API调用失败，使用模拟数据: $e');
      }

      // 如果API失败，使用模拟数据
      _questions = _getMockQuestions(quizId);
      _currentQuestionIndex = 0;
      _isLoadingQuiz = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('获取题目失败: $e');
      _isLoadingQuiz = false;
      return false;
    }
  }

  /// 获取模拟题目数据
  List<Question> _getMockQuestions(String quizId) {
    switch (quizId) {
      case '1': // MBTI人格测试
        return _getMBTIQuestions();
      case '2': // 情商测试
        return _getEQQuestions();
      case '3': // 职业兴趣测试
        return _getCareerQuestions();
      case '4': // 恋爱人格测试
        return _getLoveQuestions();
      default:
        return _getMBTIQuestions();
    }
  }

  /// 回答问题
  void answerQuestion(int questionId, dynamic answer) {
    _answers[questionId] = answer;
    notifyListeners();
  }

  /// 下一题
  void nextQuestion() {
    if (hasNextQuestion) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  /// 上一题
  void previousQuestion() {
    if (hasPreviousQuestion) {
      _currentQuestionIndex--;
      notifyListeners();
    }
  }

  /// 跳转到指定题目
  void goToQuestion(int index) {
    if (index >= 0 && index < _questions.length) {
      _currentQuestionIndex = index;
      notifyListeners();
    }
  }

  /// 提交测评
  Future<bool> submitQuiz(String quizId) async {
    _isLoadingResult = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.submitQuiz(quizId, _answers);

      if (result['success'] == true) {
        _currentResult = QuizResult.fromMap(result['data']);
        _isLoadingResult = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '提交测评失败');
        _isLoadingResult = false;
        return false;
      }
    } catch (e) {
      _setError('提交测评失败: $e');
      _isLoadingResult = false;
      return false;
    }
  }

  /// 获取测评结果
  Future<bool> fetchQuizResult(String recordId) async {
    _isLoadingResult = true;
    _clearError();
    notifyListeners();

    try {
      final result = await _quizService.getQuizResult(recordId);

      if (result['success'] == true) {
        _currentResult = QuizResult.fromMap(result['data']);
        _isLoadingResult = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? '获取结果失败');
        _isLoadingResult = false;
        return false;
      }
    } catch (e) {
      _setError('获取结果失败: $e');
      _isLoadingResult = false;
      return false;
    }
  }

  /// 获取用户历史结果
  Future<void> fetchUserResults() async {
    try {
      final result = await _quizService.getUserResults();

      if (result['success'] == true) {
        _userResults = (result['data'] as List)
            .map((json) => QuizResult.fromMap(json))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('获取历史结果失败: $e');
    }
  }

  /// 重置测评状态
  void _resetQuizState() {
    _questions.clear();
    _answers.clear();
    _currentQuestionIndex = 0;
    _currentResult = null;
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
  }

  /// 清除当前测评
  void clearCurrentQuiz() {
    _currentQuiz = null;
    _resetQuizState();
    notifyListeners();
  }

  /// MBTI人格测试题目
  List<Question> _getMBTIQuestions() {
    return [
      Question(
        id: 1,
        quizId: 1,
        questionText: '在聚会中，你更倾向于：',
        questionType: 1,
        sortOrder: 1,
        isRequired: true,
        options: [
          QuestionOption(
            id: 1,
            questionId: 1,
            optionText: '与很多人交谈',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 2,
            questionId: 1,
            optionText: '与少数几个人深入交谈',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 2,
        quizId: 1,
        questionText: '你更喜欢：',
        questionType: 1,
        sortOrder: 2,
        isRequired: true,
        options: [
          QuestionOption(
            id: 3,
            questionId: 2,
            optionText: '实际的、具体的信息',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 4,
            questionId: 2,
            optionText: '理论的、抽象的概念',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 3,
        quizId: 1,
        questionText: '做决定时，你更依赖：',
        questionType: 1,
        sortOrder: 3,
        isRequired: true,
        options: [
          QuestionOption(
            id: 5,
            questionId: 3,
            optionText: '逻辑分析',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 6,
            questionId: 3,
            optionText: '个人价值观和感受',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 4,
        quizId: 1,
        questionText: '你更喜欢：',
        questionType: 1,
        sortOrder: 4,
        isRequired: true,
        options: [
          QuestionOption(
            id: 7,
            questionId: 4,
            optionText: '有计划、有组织的生活',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 8,
            questionId: 4,
            optionText: '灵活、自发的生活',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 5,
        quizId: 1,
        questionText: '你认为自己是：',
        questionType: 1,
        sortOrder: 5,
        isRequired: true,
        options: [
          QuestionOption(
            id: 9,
            questionId: 5,
            optionText: '外向、善于社交的',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 10,
            questionId: 5,
            optionText: '内向、深思熟虑的',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
    ];
  }

  /// 情商测试题目
  List<Question> _getEQQuestions() {
    return [
      Question(
        id: 11,
        quizId: 2,
        questionText: '当朋友心情不好时，你通常会：',
        questionType: 1,
        sortOrder: 1,
        isRequired: true,
        options: [
          QuestionOption(
            id: 11,
            questionId: 11,
            optionText: '给出建议和解决方案',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 12,
            questionId: 11,
            optionText: '倾听并给予情感支持',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 12,
        quizId: 2,
        questionText: '面对压力时，你更倾向于：',
        questionType: 1,
        sortOrder: 2,
        isRequired: true,
        options: [
          QuestionOption(
            id: 13,
            questionId: 12,
            optionText: '独自处理，不想麻烦别人',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 14,
            questionId: 12,
            optionText: '寻求他人的帮助和支持',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 13,
        quizId: 2,
        questionText: '你认为情绪管理最重要的是：',
        questionType: 1,
        sortOrder: 3,
        isRequired: true,
        options: [
          QuestionOption(
            id: 15,
            questionId: 13,
            optionText: '控制和压抑负面情绪',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 16,
            questionId: 13,
            optionText: '理解和接纳所有情绪',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
    ];
  }

  /// 职业兴趣测试题目
  List<Question> _getCareerQuestions() {
    return [
      Question(
        id: 21,
        quizId: 3,
        questionText: '你更喜欢的工作环境是：',
        questionType: 1,
        sortOrder: 1,
        isRequired: true,
        options: [
          QuestionOption(
            id: 21,
            questionId: 21,
            optionText: '安静、独立的办公室',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 22,
            questionId: 21,
            optionText: '热闹、团队协作的空间',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 22,
        quizId: 3,
        questionText: '你更感兴趣的工作类型是：',
        questionType: 1,
        sortOrder: 2,
        isRequired: true,
        options: [
          QuestionOption(
            id: 23,
            questionId: 22,
            optionText: '创意设计类工作',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 24,
            questionId: 22,
            optionText: '数据分析类工作',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
    ];
  }

  /// 恋爱人格测试题目
  List<Question> _getLoveQuestions() {
    return [
      Question(
        id: 31,
        quizId: 4,
        questionText: '在恋爱关系中，你更重视：',
        questionType: 1,
        sortOrder: 1,
        isRequired: true,
        options: [
          QuestionOption(
            id: 31,
            questionId: 31,
            optionText: '精神上的契合',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 32,
            questionId: 31,
            optionText: '生活上的陪伴',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
      Question(
        id: 32,
        quizId: 4,
        questionText: '当与伴侣发生分歧时，你会：',
        questionType: 1,
        sortOrder: 2,
        isRequired: true,
        options: [
          QuestionOption(
            id: 33,
            questionId: 32,
            optionText: '坚持自己的观点',
            score: 1,
            sortOrder: 1,
          ),
          QuestionOption(
            id: 34,
            questionId: 32,
            optionText: '寻求妥协和理解',
            score: 2,
            sortOrder: 2,
          ),
        ],
      ),
    ];
  }

  /// 生成测评结果
  QuizResult generateResult(String quizId) {
    // 计算总分
    int totalScore = 0;
    for (var answer in _answers.values) {
      if (answer is int) {
        // 找到对应的选项并获取分数
        for (var question in _questions) {
          for (var option in question.options) {
            if (option.id == answer) {
              totalScore += option.score;
              break;
            }
          }
        }
      }
    }

    // 根据测评类型生成结果
    switch (quizId) {
      case '1': // MBTI人格测试
        return _generateMBTIResult(totalScore);
      case '2': // 情商测试
        return _generateEQResult(totalScore);
      case '3': // 职业兴趣测试
        return _generateCareerResult(totalScore);
      case '4': // 恋爱人格测试
        return _generateLoveResult(totalScore);
      default:
        return _generateDefaultResult(totalScore);
    }
  }

  /// 生成MBTI结果
  QuizResult _generateMBTIResult(int totalScore) {
    String personality;
    String description;
    List<String> traits;
    List<String> suggestions;

    if (totalScore <= 6) {
      personality = 'INTJ - 建筑师';
      description = '你是一个独立思考、富有创造力的人。你喜欢制定长远计划，并有能力将想法付诸实践。';
      traits = ['独立思考', '富有创造力', '目标导向', '理性分析'];
      suggestions = ['发挥你的战略思维优势', '在团队中承担规划角色', '注意与他人的情感交流'];
    } else if (totalScore <= 8) {
      personality = 'ENFP - 竞选者';
      description = '你是一个充满热情、富有想象力的人。你善于激励他人，总是能看到事物的积极面。';
      traits = ['热情洋溢', '富有想象力', '善于激励', '乐观积极'];
      suggestions = ['利用你的人际交往能力', '在创意工作中发挥优势', '学会专注于重要目标'];
    } else {
      personality = 'ISFJ - 守护者';
      description = '你是一个温暖、负责任的人。你关心他人的需要，总是愿意提供帮助和支持。';
      traits = ['温暖体贴', '责任心强', '关心他人', '可靠稳定'];
      suggestions = ['发挥你的服务精神', '在支持性角色中表现出色', '记得也要关心自己的需求'];
    }

    return QuizResult(
      id: DateTime.now().millisecondsSinceEpoch,
      userId: 1,
      quizId: 1,
      quizTitle: 'MBTI人格测试',
      startTime: DateTime.now().subtract(const Duration(minutes: 15)),
      endTime: DateTime.now(),
      totalScore: totalScore,
      resultType: personality,
      resultTitle: personality,
      resultDescription: description,
      detailedAnalysis: traits.join('、'),
      suggestions: suggestions.join('；'),
      status: 1,
    );
  }

  /// 生成情商测试结果
  QuizResult _generateEQResult(int totalScore) {
    String level;
    String description;
    List<String> suggestions;

    if (totalScore <= 4) {
      level = '情商较低';
      description = '你在情绪管理和人际交往方面还有很大的提升空间。建议多关注自己和他人的情绪变化。';
      suggestions = ['学习情绪管理技巧', '多观察他人的情绪表达', '练习换位思考'];
    } else if (totalScore <= 5) {
      level = '情商中等';
      description = '你具备基本的情绪管理能力，在人际交往中表现良好。继续提升会让你更加出色。';
      suggestions = ['继续提升同理心', '学习更多沟通技巧', '在冲突中保持冷静'];
    } else {
      level = '情商较高';
      description = '你拥有出色的情绪管理能力和人际交往技巧。你能很好地理解和处理各种情绪。';
      suggestions = ['发挥你的领导潜力', '帮助他人提升情商', '在团队中发挥协调作用'];
    }

    return QuizResult(
      id: DateTime.now().millisecondsSinceEpoch,
      userId: 1,
      quizId: 2,
      quizTitle: '情商测试',
      startTime: DateTime.now().subtract(const Duration(minutes: 10)),
      endTime: DateTime.now(),
      totalScore: totalScore,
      resultType: level,
      resultTitle: '你的情商水平：$level',
      resultDescription: description,
      detailedAnalysis: '情绪管理、人际交往、同理心',
      suggestions: suggestions.join('；'),
      status: 1,
    );
  }

  /// 生成职业兴趣结果
  QuizResult _generateCareerResult(int totalScore) {
    String careerType;
    String description;
    List<String> suggestions;

    if (totalScore <= 3) {
      careerType = '研究型';
      description = '你喜欢独立工作，善于分析和解决复杂问题。适合从事研究、技术或学术相关工作。';
      suggestions = ['考虑科研或技术岗位', '发挥你的分析能力', '在专业领域深入发展'];
    } else {
      careerType = '社会型';
      description = '你喜欢与人合作，善于沟通和协调。适合从事教育、咨询或管理相关工作。';
      suggestions = ['考虑教育或咨询行业', '发挥你的人际优势', '在团队管理中表现出色'];
    }

    return QuizResult(
      id: DateTime.now().millisecondsSinceEpoch,
      userId: 1,
      quizId: 3,
      quizTitle: '职业兴趣测试',
      startTime: DateTime.now().subtract(const Duration(minutes: 12)),
      endTime: DateTime.now(),
      totalScore: totalScore,
      resultType: careerType,
      resultTitle: '你的职业类型：$careerType',
      resultDescription: description,
      detailedAnalysis: '工作偏好、环境适应、发展方向',
      suggestions: suggestions.join('；'),
      status: 1,
    );
  }

  /// 生成恋爱人格结果
  QuizResult _generateLoveResult(int totalScore) {
    String loveType;
    String description;
    List<String> suggestions;

    if (totalScore <= 3) {
      loveType = '理性型恋人';
      description = '你在恋爱中比较理性，重视精神契合和独立空间。你希望与伴侣保持平等的关系。';
      suggestions = ['保持理性的同时增加感性表达', '学会更多情感沟通', '适当表达关爱和依赖'];
    } else {
      loveType = '感性型恋人';
      description = '你在恋爱中比较感性，重视情感交流和陪伴。你愿意为了关系做出妥协和调整。';
      suggestions = ['在感性的基础上增加理性思考', '保持个人独立性', '学会适度表达需求'];
    }

    return QuizResult(
      id: DateTime.now().millisecondsSinceEpoch,
      userId: 1,
      quizId: 4,
      quizTitle: '恋爱人格测试',
      startTime: DateTime.now().subtract(const Duration(minutes: 8)),
      endTime: DateTime.now(),
      totalScore: totalScore,
      resultType: loveType,
      resultTitle: '你的恋爱类型：$loveType',
      resultDescription: description,
      detailedAnalysis: '恋爱观念、相处模式、沟通方式',
      suggestions: suggestions.join('；'),
      status: 1,
    );
  }

  /// 生成默认结果
  QuizResult _generateDefaultResult(int totalScore) {
    return QuizResult(
      id: DateTime.now().millisecondsSinceEpoch,
      userId: 1,
      quizId: 0,
      quizTitle: '综合测评',
      startTime: DateTime.now().subtract(const Duration(minutes: 10)),
      endTime: DateTime.now(),
      totalScore: totalScore,
      resultType: '综合评估',
      resultTitle: '测评完成',
      resultDescription: '感谢您完成本次测评，这是您的综合评估结果。',
      detailedAnalysis: '综合分析',
      suggestions: '继续保持；持续改进；全面发展',
      status: 1,
    );
  }
}
