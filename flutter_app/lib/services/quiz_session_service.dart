import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quiz_session.dart';

class QuizSessionService {
  static const String _sessionsKey = 'quiz_sessions';
  
  static QuizSessionService? _instance;
  static QuizSessionService get instance {
    _instance ??= QuizSessionService._();
    return _instance!;
  }
  
  QuizSessionService._();

  // 获取所有答题会话
  Future<List<QuizSession>> getAllSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = prefs.getString(_sessionsKey);
    
    if (sessionsJson == null) return [];
    
    final sessionsList = jsonDecode(sessionsJson) as List;
    return sessionsList
        .map((json) => QuizSession.fromJson(json))
        .toList()
        ..sort((a, b) => b.lastAnswerTime?.compareTo(a.lastAnswerTime ?? a.startTime) ?? 0);
  }

  // 获取指定测评的会话
  Future<List<QuizSession>> getSessionsForQuiz(String quizId) async {
    final allSessions = await getAllSessions();
    return allSessions.where((session) => session.quizId == quizId).toList();
  }

  // 获取指定测评的未完成会话
  Future<QuizSession?> getIncompleteSessionForQuiz(String quizId) async {
    final sessions = await getSessionsForQuiz(quizId);
    try {
      return sessions.firstWhere((session) => !session.isCompleted);
    } catch (e) {
      return null;
    }
  }

  // 保存或更新会话
  Future<void> saveSession(QuizSession session) async {
    final allSessions = await getAllSessions();
    final existingIndex = allSessions.indexWhere((s) => s.id == session.id);
    
    if (existingIndex >= 0) {
      allSessions[existingIndex] = session;
    } else {
      allSessions.add(session);
    }
    
    await _saveSessions(allSessions);
  }

  // 创建新会话
  Future<QuizSession> createSession({
    required String quizId,
    required String quizTitle,
    required int totalQuestions,
  }) async {
    final session = QuizSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      quizId: quizId,
      quizTitle: quizTitle,
      startTime: DateTime.now(),
      isCompleted: false,
      currentQuestionIndex: 0,
      answers: {},
      totalQuestions: totalQuestions,
      progress: 0.0,
    );
    
    await saveSession(session);
    return session;
  }

  // 更新会话答案
  Future<void> updateSessionAnswer({
    required String sessionId,
    required int questionIndex,
    required int selectedOptionId,
  }) async {
    final allSessions = await getAllSessions();
    final sessionIndex = allSessions.indexWhere((s) => s.id == sessionId);
    
    if (sessionIndex >= 0) {
      final session = allSessions[sessionIndex];
      final updatedAnswers = Map<int, int>.from(session.answers);
      updatedAnswers[questionIndex] = selectedOptionId;
      
      final updatedSession = session.copyWith(
        answers: updatedAnswers,
        lastAnswerTime: DateTime.now(),
        progress: updatedAnswers.length / session.totalQuestions,
        currentQuestionIndex: questionIndex + 1 < session.totalQuestions 
            ? questionIndex + 1 
            : session.currentQuestionIndex,
      );
      
      allSessions[sessionIndex] = updatedSession;
      await _saveSessions(allSessions);
    }
  }

  // 完成会话
  Future<void> completeSession(String sessionId) async {
    final allSessions = await getAllSessions();
    final sessionIndex = allSessions.indexWhere((s) => s.id == sessionId);
    
    if (sessionIndex >= 0) {
      final session = allSessions[sessionIndex];
      final completedSession = session.copyWith(
        isCompleted: true,
        lastAnswerTime: DateTime.now(),
        progress: 1.0,
      );
      
      allSessions[sessionIndex] = completedSession;
      await _saveSessions(allSessions);
    }
  }

  // 删除会话
  Future<void> deleteSession(String sessionId) async {
    final allSessions = await getAllSessions();
    allSessions.removeWhere((s) => s.id == sessionId);
    await _saveSessions(allSessions);
  }

  // 跳转到指定题目
  Future<void> jumpToQuestion({
    required String sessionId,
    required int questionIndex,
  }) async {
    final allSessions = await getAllSessions();
    final sessionIndex = allSessions.indexWhere((s) => s.id == sessionId);
    
    if (sessionIndex >= 0) {
      final session = allSessions[sessionIndex];
      final updatedSession = session.copyWith(
        currentQuestionIndex: questionIndex,
        lastAnswerTime: DateTime.now(),
      );
      
      allSessions[sessionIndex] = updatedSession;
      await _saveSessions(allSessions);
    }
  }

  // 私有方法：保存所有会话
  Future<void> _saveSessions(List<QuizSession> sessions) async {
    final prefs = await SharedPreferences.getInstance();
    final sessionsJson = jsonEncode(sessions.map((s) => s.toJson()).toList());
    await prefs.setString(_sessionsKey, sessionsJson);
  }

  // 清除所有会话（用于测试）
  Future<void> clearAllSessions() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_sessionsKey);
  }
}
