import 'api_service.dart';

class QuizService {
  final ApiService _apiService = ApiService.instance;

  /// 获取测评列表
  Future<Map<String, dynamic>> getQuizzes({
    String? category,
    int page = 1,
    int pageSize = 20,
  }) async {
    return await _apiService.get('/quiz/list', queryParameters: {
      if (category != null) 'category': category,
      'page': page,
      'pageSize': pageSize,
    });
  }

  /// 获取推荐测评
  Future<Map<String, dynamic>> getFeaturedQuizzes() async {
    return await _apiService.get('/quiz/featured');
  }

  /// 获取测评详情
  Future<Map<String, dynamic>> getQuizDetail(String quizId) async {
    return await _apiService.get('/quiz/$quizId');
  }

  /// 获取测评题目
  Future<Map<String, dynamic>> getQuizQuestions(String quizId) async {
    return await _apiService.get('/quiz/$quizId/questions');
  }

  /// 开始测评
  Future<Map<String, dynamic>> startQuiz(String quizId) async {
    return await _apiService.post('/quiz/$quizId/start');
  }

  /// 保存单个答案
  Future<Map<String, dynamic>> saveAnswer(
    String recordId,
    int questionId,
    dynamic answer,
  ) async {
    return await _apiService.post('/answer/save', data: {
      'recordId': recordId,
      'questionId': questionId,
      'answer': answer,
    });
  }

  /// 提交答案（已废弃，使用saveAnswer）
  Future<Map<String, dynamic>> submitAnswer(
    String recordId,
    int questionId,
    dynamic answer,
  ) async {
    return await _apiService.post('/answer/submit', data: {
      'recordId': recordId,
      'questionId': questionId,
      'answer': answer,
    });
  }

  /// 提交整个测评
  Future<Map<String, dynamic>> submitQuiz(
    String recordId,
    Map<int, dynamic> answers,
  ) async {
    return await _apiService.post('/answer/submit', data: {
      'recordId': recordId,
      'answers': answers,
    });
  }

  /// 获取测评结果
  Future<Map<String, dynamic>> getQuizResult(String recordId) async {
    return await _apiService.get('/result/$recordId');
  }

  /// 获取用户历史结果
  Future<Map<String, dynamic>> getUserResults({
    int page = 1,
    int pageSize = 20,
  }) async {
    return await _apiService.get('/user/results', queryParameters: {
      'page': page,
      'pageSize': pageSize,
    });
  }

  /// 收藏测评
  Future<Map<String, dynamic>> favoriteQuiz(String quizId) async {
    return await _apiService.post('/user/favorite', data: {
      'quizId': quizId,
    });
  }

  /// 取消收藏
  Future<Map<String, dynamic>> unfavoriteQuiz(String quizId) async {
    return await _apiService.delete('/user/favorite', queryParameters: {
      'quizId': quizId,
    });
  }

  /// 获取收藏列表
  Future<Map<String, dynamic>> getFavoriteQuizzes() async {
    return await _apiService.get('/user/favorites');
  }

  /// 分享测评结果
  Future<Map<String, dynamic>> shareResult(String recordId) async {
    return await _apiService.post('/result/$recordId/share');
  }

  /// 获取测评分类
  Future<Map<String, dynamic>> getCategories() async {
    return await _apiService.get('/quiz/categories');
  }

  /// 搜索测评
  Future<Map<String, dynamic>> searchQuizzes(
    String keyword, {
    int page = 1,
    int pageSize = 20,
  }) async {
    return await _apiService.get('/quiz/search', queryParameters: {
      'keyword': keyword,
      'page': page,
      'pageSize': pageSize,
    });
  }
}
