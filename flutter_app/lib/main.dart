import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'providers/auth_provider.dart';
import 'providers/quiz_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/home/<USER>';
import 'screens/quiz/quiz_list_screen.dart';
import 'screens/quiz/quiz_detail_screen.dart';
import 'screens/quiz/quiz_taking_screen.dart';
import 'screens/quiz/quiz_result_screen.dart';
import 'screens/profile/profile_screen.dart';
import 'utils/app_theme.dart';

void main() {
  runApp(const QuizApp());
}

class QuizApp extends StatelessWidget {
  const QuizApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => QuizProvider()),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp.router(
            title: 'QUIZ心理测评',
            theme: AppTheme.lightTheme,
            debugShowCheckedModeBanner: false,
            routerConfig: _createRouter(authProvider),
          );
        },
      ),
    );
  }

  GoRouter _createRouter(AuthProvider authProvider) {
    return GoRouter(
      initialLocation: '/home',
      routes: [
        GoRoute(
          path: '/splash',
          builder: (context, state) => const SplashScreen(),
        ),
        GoRoute(
          path: '/login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/home',
          builder: (context, state) => const HomeScreen(),
        ),
        GoRoute(
          path: '/quiz-list',
          builder: (context, state) => const QuizListScreen(),
        ),
        GoRoute(
          path: '/quiz-detail/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return QuizDetailScreen(quizId: id);
          },
        ),
        GoRoute(
          path: '/quiz-taking/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return QuizTakingScreen(quizId: id);
          },
        ),
        GoRoute(
          path: '/quiz-result/:recordId',
          builder: (context, state) {
            final recordId = state.pathParameters['recordId']!;
            return QuizResultScreen(recordId: recordId);
          },
        ),
        GoRoute(
          path: '/profile',
          builder: (context, state) => const ProfileScreen(),
        ),
      ],
    );
  }
}
