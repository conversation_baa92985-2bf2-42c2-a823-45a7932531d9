class QuizSession {
  final String id;
  final String quizId;
  final String quizTitle;
  final DateTime startTime;
  final DateTime? lastAnswerTime;
  final bool isCompleted;
  final int currentQuestionIndex;
  final Map<int, int> answers; // questionIndex -> selectedOptionId
  final int totalQuestions;
  final double progress;

  QuizSession({
    required this.id,
    required this.quizId,
    required this.quizTitle,
    required this.startTime,
    this.lastAnswerTime,
    required this.isCompleted,
    required this.currentQuestionIndex,
    required this.answers,
    required this.totalQuestions,
    required this.progress,
  });

  factory QuizSession.fromJson(Map<String, dynamic> json) {
    return QuizSession(
      id: json['id'],
      quizId: json['quizId'],
      quizTitle: json['quizTitle'],
      startTime: DateTime.parse(json['startTime']),
      lastAnswerTime: json['lastAnswerTime'] != null 
          ? DateTime.parse(json['lastAnswerTime']) 
          : null,
      isCompleted: json['isCompleted'],
      currentQuestionIndex: json['currentQuestionIndex'],
      answers: Map<int, int>.from(json['answers']),
      totalQuestions: json['totalQuestions'],
      progress: json['progress'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quizId': quizId,
      'quizTitle': quizTitle,
      'startTime': startTime.toIso8601String(),
      'lastAnswerTime': lastAnswerTime?.toIso8601String(),
      'isCompleted': isCompleted,
      'currentQuestionIndex': currentQuestionIndex,
      'answers': answers,
      'totalQuestions': totalQuestions,
      'progress': progress,
    };
  }

  QuizSession copyWith({
    String? id,
    String? quizId,
    String? quizTitle,
    DateTime? startTime,
    DateTime? lastAnswerTime,
    bool? isCompleted,
    int? currentQuestionIndex,
    Map<int, int>? answers,
    int? totalQuestions,
    double? progress,
  }) {
    return QuizSession(
      id: id ?? this.id,
      quizId: quizId ?? this.quizId,
      quizTitle: quizTitle ?? this.quizTitle,
      startTime: startTime ?? this.startTime,
      lastAnswerTime: lastAnswerTime ?? this.lastAnswerTime,
      isCompleted: isCompleted ?? this.isCompleted,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      answers: answers ?? this.answers,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      progress: progress ?? this.progress,
    );
  }

  String get progressText {
    return '${answers.length}/$totalQuestions';
  }

  String get timeText {
    final now = DateTime.now();
    final difference = now.difference(lastAnswerTime ?? startTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
