import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_theme.dart';
import '../../widgets/custom_button.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人中心'),
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.textPrimary,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.user;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // 用户信息卡片
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: AppTheme.cardDecoration,
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppTheme.lavender.withOpacity(0.1),
                        child: user?.avatarUrl != null
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: Image.network(
                                  user!.avatarUrl!,
                                  width: 60,
                                  height: 60,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : const Icon(
                                Icons.person,
                                color: AppTheme.lavender,
                                size: 35,
                              ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user?.nickname ?? '用户',
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user?.email ?? '',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: user?.isVipValid == true
                                    ? AppTheme.lightOrange.withOpacity(0.2)
                                    : AppTheme.lightGray,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                user?.isVipValid == true ? 'VIP会员' : '普通用户',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: user?.isVipValid == true
                                          ? AppTheme.warning
                                          : AppTheme.textSecondary,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // 功能菜单
                Container(
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    children: [
                      _buildMenuItem(
                        context,
                        icon: Icons.assessment,
                        title: '我的测评',
                        subtitle: '查看历史测评记录',
                        onTap: () {
                          context.go('/reports');
                        },
                      ),
                      _buildDivider(),
                      _buildMenuItem(
                        context,
                        icon: Icons.favorite,
                        title: '我的收藏',
                        subtitle: '收藏的测评内容',
                        onTap: () {
                          // TODO: 跳转到收藏页面
                        },
                      ),
                      _buildDivider(),
                      _buildMenuItem(
                        context,
                        icon: Icons.settings,
                        title: '设置',
                        subtitle: '个人信息和应用设置',
                        onTap: () {
                          // TODO: 跳转到设置页面
                        },
                      ),
                      _buildDivider(),
                      _buildMenuItem(
                        context,
                        icon: Icons.help,
                        title: '帮助与反馈',
                        subtitle: '使用帮助和意见反馈',
                        onTap: () {
                          // TODO: 跳转到帮助页面
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // 退出登录按钮
                CustomButton(
                  text: '退出登录',
                  onPressed: () async {
                    await authProvider.logout();
                    if (context.mounted) {
                      context.go('/login');
                    }
                  },
                  backgroundColor: AppTheme.error,
                  textColor: Colors.white,
                  width: double.infinity,
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppTheme.lavender.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.lavender,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.chevron_right,
                color: AppTheme.textLight,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: Colors.grey.shade200,
      indent: 72,
    );
  }
}
