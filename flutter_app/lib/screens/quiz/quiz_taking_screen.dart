import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/quiz_provider.dart';
import '../../utils/app_theme.dart';

class QuizTakingScreen extends StatefulWidget {
  final String quizId;

  const QuizTakingScreen({super.key, required this.quizId});

  @override
  State<QuizTakingScreen> createState() => _QuizTakingScreenState();
}

class _QuizTakingScreenState extends State<QuizTakingScreen> {
  int? selectedOptionId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startQuiz();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    _updateSelectedOption(quizProvider);
  }

  void _startQuiz() {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    quizProvider.startQuiz(widget.quizId);
  }

  void _updateSelectedOption(QuizProvider quizProvider) {
    // 恢复当前题目的已选答案
    if (quizProvider.answers.containsKey(quizProvider.currentQuestionIndex)) {
      setState(() {
        selectedOptionId =
            quizProvider.answers[quizProvider.currentQuestionIndex];
      });
    } else {
      setState(() {
        selectedOptionId = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<QuizProvider>(
      builder: (context, quizProvider, child) {
        if (quizProvider.isLoadingQuiz) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('加载中...'),
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.textPrimary,
              elevation: 0,
            ),
            body: const Center(
              child: CircularProgressIndicator(
                color: AppTheme.lavender,
              ),
            ),
          );
        }

        if (quizProvider.error != null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('测评'),
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.textPrimary,
              elevation: 0,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    quizProvider.error!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _startQuiz,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.lavender,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('重试'),
                  ),
                ],
              ),
            ),
          );
        }

        if (quizProvider.questions.isEmpty) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('测评'),
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.textPrimary,
              elevation: 0,
            ),
            body: const Center(
              child: Text(
                '暂无题目',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                ),
              ),
            ),
          );
        }

        final currentQuestion = quizProvider.currentQuestion;
        if (currentQuestion == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('测评完成'),
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.textPrimary,
              elevation: 0,
            ),
            body: const Center(
              child: Text(
                '测评已完成',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                ),
              ),
            ),
          );
        }

        return Scaffold(
          backgroundColor: const Color(0xFFF5F5F5),
          appBar: AppBar(
            title: Text('第${quizProvider.currentQuestionIndex + 1}题'),
            backgroundColor: Colors.white,
            foregroundColor: AppTheme.textPrimary,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => _showExitDialog(context, quizProvider),
            ),
            actions: [
              // 题目导航按钮
              IconButton(
                icon: const Icon(Icons.list),
                onPressed: () => _showQuestionNavigator(context, quizProvider),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Center(
                  child: Text(
                    '${quizProvider.currentQuestionIndex + 1}/${quizProvider.questions.length}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Column(
            children: [
              // 进度条
              Container(
                height: 4,
                margin: const EdgeInsets.symmetric(horizontal: 20),
                child: LinearProgressIndicator(
                  value: quizProvider.progress,
                  backgroundColor: Colors.grey.shade200,
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(AppTheme.lavender),
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 题目
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(24),
                        decoration: AppTheme.cardDecoration,
                        child: Text(
                          currentQuestion.questionText,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimary,
                            height: 1.5,
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // 选项
                      ...currentQuestion.options.map((option) {
                        final isSelected = selectedOptionId == option.id;
                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16),
                              onTap: () async {
                                setState(() {
                                  selectedOptionId = option.id;
                                });

                                // 保存答案到会话
                                await quizProvider.answerQuestion(
                                    quizProvider.currentQuestionIndex,
                                    option.id);
                              },
                              child: Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? AppTheme.lavender.withOpacity(0.1)
                                      : Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: isSelected
                                        ? AppTheme.lavender
                                        : Colors.grey.shade200,
                                    width: isSelected ? 2 : 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 10,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 24,
                                      height: 24,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: isSelected
                                              ? AppTheme.lavender
                                              : Colors.grey.shade400,
                                          width: 2,
                                        ),
                                        color: isSelected
                                            ? AppTheme.lavender
                                            : Colors.transparent,
                                      ),
                                      child: isSelected
                                          ? const Icon(
                                              Icons.check,
                                              size: 16,
                                              color: Colors.white,
                                            )
                                          : null,
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Text(
                                        option.optionText,
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: isSelected
                                              ? AppTheme.lavender
                                              : AppTheme.textPrimary,
                                          fontWeight: isSelected
                                              ? FontWeight.w600
                                              : FontWeight.normal,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),

              // 底部按钮
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    if (quizProvider.hasPreviousQuestion)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            quizProvider.previousQuestion();
                            _updateSelectedOption(quizProvider);
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppTheme.lavender,
                            side: const BorderSide(color: AppTheme.lavender),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('上一题'),
                        ),
                      ),
                    if (quizProvider.hasPreviousQuestion)
                      const SizedBox(width: 12),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: selectedOptionId != null
                            ? () async {
                                // 保存答案
                                quizProvider.answerQuestion(
                                    currentQuestion.id, selectedOptionId);

                                if (quizProvider.hasNextQuestion) {
                                  // 下一题
                                  quizProvider.nextQuestion();
                                  _updateSelectedOption(quizProvider);
                                } else {
                                  // 完成会话
                                  await quizProvider.completeCurrentSession();
                                  // 完成测评，生成结果并跳转到结果页
                                  try {
                                    final result = await quizProvider
                                        .generateResult(widget.quizId);
                                    if (mounted) {
                                      context.go('/quiz-result/${result.id}');
                                    }
                                  } catch (e) {
                                    if (mounted) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(content: Text('提交测评失败: $e')),
                                      );
                                    }
                                  }
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.lavender,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          disabledBackgroundColor: Colors.grey.shade300,
                        ),
                        child: Text(
                          quizProvider.hasNextQuestion ? '下一题' : '完成测评',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showExitDialog(BuildContext context, QuizProvider quizProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('退出测评'),
          content: const Text('您的答题进度已自动保存，可以随时回来继续完成。确定要退出吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.go('/quiz-detail/${widget.quizId}');
              },
              child: const Text('确定退出'),
            ),
          ],
        );
      },
    );
  }

  void _showQuestionNavigator(BuildContext context, QuizProvider quizProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: Color(0xFFE6D4EF),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '题目导航',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            final nextIndex =
                                quizProvider.nextUnAnsweredQuestionIndex;
                            await quizProvider.jumpToQuestion(nextIndex);
                            // 确保状态更新后再恢复选项
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              _updateSelectedOption(quizProvider);
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: const Color(0xFFE6D4EF),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                          ),
                          child: const Text('继续答题',
                              style: TextStyle(fontSize: 12)),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 题目网格
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: GridView.builder(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1,
                    ),
                    itemCount: quizProvider.questions.length,
                    itemBuilder: (context, index) {
                      final isAnswered =
                          quizProvider.answers.containsKey(index);
                      final isCurrent =
                          index == quizProvider.currentQuestionIndex;

                      return GestureDetector(
                        onTap: () async {
                          Navigator.of(context).pop();
                          await quizProvider.jumpToQuestion(index);
                          // 确保状态更新后再恢复选项
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _updateSelectedOption(quizProvider);
                          });
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: isCurrent
                                ? const Color(0xFFE6D4EF)
                                : isAnswered
                                    ? Colors.green.shade100
                                    : Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isCurrent
                                  ? const Color(0xFFE6D4EF)
                                  : isAnswered
                                      ? Colors.green
                                      : Colors.grey.shade400,
                              width: 2,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              '${index + 1}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isCurrent
                                    ? Colors.white
                                    : isAnswered
                                        ? Colors.green.shade700
                                        : Colors.grey.shade600,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
