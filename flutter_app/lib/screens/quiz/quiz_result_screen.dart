import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/quiz_provider.dart';
import '../../utils/app_theme.dart';
import '../../models/quiz_result.dart';

class QuizResultScreen extends StatefulWidget {
  final String recordId;

  const QuizResultScreen({super.key, required this.recordId});

  @override
  State<QuizResultScreen> createState() => _QuizResultScreenState();
}

class _QuizResultScreenState extends State<QuizResultScreen> {
  QuizResult? result;

  @override
  void initState() {
    super.initState();
    _loadResult();
  }

  void _loadResult() {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    // 这里应该从provider或服务中获取结果，现在我们使用模拟数据
    setState(() {
      result = _getMockResult();
    });
  }

  QuizResult _getMockResult() {
    // 根据recordId生成对应的模拟结果
    int id;
    try {
      id = int.parse(widget.recordId);
    } catch (e) {
      // 如果无法解析为数字，使用默认值
      id = DateTime.now().millisecondsSinceEpoch;
    }

    return QuizResult(
      id: id,
      userId: 1,
      quizId: 1,
      quizTitle: 'MBTI人格测试',
      startTime: DateTime.now().subtract(const Duration(minutes: 15)),
      endTime: DateTime.now(),
      totalScore: 7,
      resultType: 'ENFP - 竞选者',
      resultTitle: 'ENFP - 竞选者',
      resultDescription: '你是一个充满热情、富有想象力的人。你善于激励他人，总是能看到事物的积极面。',
      detailedAnalysis: '热情洋溢、富有想象力、善于激励、乐观积极',
      suggestions: '利用你的人际交往能力；在创意工作中发挥优势；学会专注于重要目标',
      status: 1,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (result == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('测评结果'),
          backgroundColor: Colors.white,
          foregroundColor: AppTheme.textPrimary,
          elevation: 0,
        ),
        body: const Center(
          child: CircularProgressIndicator(
            color: AppTheme.lavender,
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text('测评结果'),
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.textPrimary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              // 分享功能
              _shareResult();
            },
            icon: const Icon(Icons.share),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 结果卡片
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: AppTheme.cardDecoration.copyWith(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.lavender.withOpacity(0.1),
                    AppTheme.mintGreen.withOpacity(0.1),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // 结果图标
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppTheme.lavender,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.lavender.withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.psychology,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // 结果标题
                  Text(
                    result!.resultTitle ?? '',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // 测评名称
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppTheme.lavender.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      result!.quizTitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.lavender,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // 分数显示
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildScoreItem(
                          '总分', '${result!.totalScore}', AppTheme.lavender),
                      const SizedBox(width: 40),
                      _buildScoreItem('用时', '15分钟', AppTheme.mintGreen),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 结果描述
            _buildSection(
              '结果解读',
              Icons.description,
              result!.resultDescription ?? '',
            ),

            const SizedBox(height: 20),

            // 详细分析
            _buildSection(
              '特质分析',
              Icons.analytics,
              result!.detailedAnalysis ?? '',
            ),

            const SizedBox(height: 20),

            // 建议
            _buildSection(
              '发展建议',
              Icons.lightbulb,
              result!.suggestions ?? '',
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  context.go('/quiz-list');
                },
                icon: const Icon(Icons.quiz),
                label: const Text('更多测评'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.lavender,
                  side: const BorderSide(color: AppTheme.lavender),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _shareResult();
                },
                icon: const Icon(Icons.share),
                label: const Text('分享结果'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.lavender,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildSection(String title, IconData icon, String content) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: AppTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.lavender.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: AppTheme.lavender,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textPrimary,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  void _shareResult() {
    // 分享功能实现
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('分享结果'),
        content: const Text('分享功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
