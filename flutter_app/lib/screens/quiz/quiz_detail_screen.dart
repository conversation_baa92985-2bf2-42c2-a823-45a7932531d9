import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../providers/quiz_provider.dart';
import '../../models/quiz_session.dart';

class QuizDetailScreen extends StatefulWidget {
  final String quizId;

  const QuizDetailScreen({super.key, required this.quizId});

  @override
  State<QuizDetailScreen> createState() => _QuizDetailScreenState();
}

class _QuizDetailScreenState extends State<QuizDetailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final quizProvider = Provider.of<QuizProvider>(context, listen: false);
      quizProvider.loadQuizSessions(widget.quizId);
    });
  }

  // 模拟测评数据
  Map<String, dynamic> get quizData {
    switch (widget.quizId) {
      case '1':
        return {
          'title': 'MBTI人格测试',
          'subtitle': '16型人格测评',
          'description':
              '通过科学的心理学测评，了解你的人格类型和行为偏好。MBTI是基于荣格心理类型理论开发的人格测评工具，能够帮助你更好地认识自己。',
          'questionCount': 60,
          'estimatedTime': 15,
          'category': '人格测试',
          'isFree': true,
        };
      case '2':
        return {
          'title': '情商测试',
          'subtitle': 'EQ情绪智力测评',
          'description': '评估你的情绪智力水平，了解自己在情绪管理、人际交往等方面的能力。情商是成功的重要因素之一。',
          'questionCount': 40,
          'estimatedTime': 10,
          'category': '情商测试',
          'isFree': true,
        };
      case '3':
        return {
          'title': '职业兴趣测试',
          'subtitle': '霍兰德职业测评',
          'description': '基于霍兰德职业兴趣理论，帮助你发现适合的职业方向和工作环境，为职业规划提供科学依据。',
          'questionCount': 50,
          'estimatedTime': 12,
          'category': '职业测试',
          'isFree': true,
        };
      case '4':
        return {
          'title': '恋爱人格测试',
          'subtitle': '恋爱观测评',
          'description': '了解你在恋爱关系中的行为模式和偏好，帮助你建立更健康的亲密关系。',
          'questionCount': 30,
          'estimatedTime': 8,
          'category': '恋爱测试',
          'isFree': true,
        };
      default:
        return {
          'title': '心理测评',
          'subtitle': '专业心理测评',
          'description': '专业的心理测评工具，帮助你更好地了解自己。',
          'questionCount': 20,
          'estimatedTime': 5,
          'category': '心理测试',
          'isFree': true,
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    final quiz = quizData;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            backgroundColor: const Color(0xFFE6D4EF),
            foregroundColor: Colors.white,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () {
                if (context.canPop()) {
                  context.pop();
                } else {
                  context.go('/quiz-list');
                }
              },
            ),
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                quiz['title'],
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFFE6D4EF), // 薰衣草紫
                      Color(0xFFD1E5E9), // 薄荷绿
                    ],
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.psychology,
                    size: 80,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 基本信息卡片
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          quiz['subtitle'],
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xFF7F8C8D),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          quiz['description'],
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xFF2C3E50),
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 20),
                        Row(
                          children: [
                            _buildInfoItem(
                              Icons.quiz,
                              '${quiz['questionCount']}题',
                              const Color(0xFFE6D4EF),
                            ),
                            const SizedBox(width: 20),
                            _buildInfoItem(
                              Icons.access_time,
                              '${quiz['estimatedTime']}分钟',
                              const Color(0xFFD1E5E9),
                            ),
                            const SizedBox(width: 20),
                            _buildInfoItem(
                              Icons.category,
                              quiz['category'],
                              const Color(0xFFFFE4CC),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 测评说明
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '测评说明',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2C3E50),
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildInstructionItem('1. 请在安静的环境中完成测评'),
                        _buildInstructionItem('2. 根据第一感觉选择答案'),
                        _buildInstructionItem('3. 请诚实回答所有问题'),
                        _buildInstructionItem('4. 测评结果仅供参考'),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // 历史答题记录
                  Consumer<QuizProvider>(
                    builder: (context, quizProvider, child) {
                      if (quizProvider.isLoadingSessions) {
                        return const Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFFE6D4EF),
                          ),
                        );
                      }

                      if (quizProvider.quizSessions.isNotEmpty) {
                        return Container(
                          margin: const EdgeInsets.only(bottom: 24),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '答题记录',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              const SizedBox(height: 16),
                              ...quizProvider.quizSessions
                                  .map((session) => _buildSessionItem(
                                      context, session, quizProvider))
                                  .toList(),
                            ],
                          ),
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),

                  // 开始按钮
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: () {
                        context.go('/quiz-taking/${widget.quizId}');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFE6D4EF),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 4,
                      ),
                      child: const Text(
                        '开始测评',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: const BoxDecoration(
              color: Color(0xFFE6D4EF),
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF2C3E50),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionItem(
      BuildContext context, QuizSession session, QuizProvider quizProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: session.isCompleted
            ? const Color(0xFFF8F9FA)
            : const Color(0xFFE6D4EF).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: session.isCompleted
              ? Colors.grey.shade300
              : const Color(0xFFE6D4EF),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 状态图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color:
                  session.isCompleted ? Colors.green : const Color(0xFFE6D4EF),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              session.isCompleted ? Icons.check : Icons.play_arrow,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),

          // 会话信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  session.isCompleted ? '已完成' : '进行中',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: session.isCompleted
                        ? Colors.green
                        : const Color(0xFFE6D4EF),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '进度: ${session.progressText} • ${session.timeText}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF666666),
                  ),
                ),
                if (session.progress > 0 && session.progress < 1)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: session.progress,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFE6D4EF),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // 操作按钮
          if (!session.isCompleted)
            ElevatedButton(
              onPressed: () async {
                await quizProvider.resumeSession(session);
                if (context.mounted) {
                  context.go('/quiz-taking/${widget.quizId}');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE6D4EF),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '继续',
                style: TextStyle(fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }
}
