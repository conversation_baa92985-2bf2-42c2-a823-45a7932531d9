# QUIZ Flutter App

## 项目概述
QUIZ心理测评移动应用，基于Flutter开发，优先适配iOS平台。

## 功能特性
- 🔐 用户认证（登录/注册）
- 🏠 首页推荐和分类浏览
- 📝 心理测评答题功能
- 📊 测评结果展示和分析
- 👤 个人中心和设置
- 💎 VIP会员功能

## 技术栈
- **框架**: Flutter 3.x
- **状态管理**: Provider
- **路由管理**: GoRouter
- **网络请求**: Dio
- **本地存储**: SharedPreferences
- **UI设计**: 自定义主题（柔和粉彩风格）

## 项目结构
```
lib/
├── main.dart              # 应用入口
├── models/               # 数据模型
│   ├── user.dart
│   ├── quiz.dart
│   ├── question.dart
│   └── quiz_result.dart
├── providers/            # 状态管理
│   ├── auth_provider.dart
│   └── quiz_provider.dart
├── services/             # 网络服务
│   ├── api_service.dart
│   ├── auth_service.dart
│   └── quiz_service.dart
├── screens/              # 页面
│   ├── splash_screen.dart
│   ├── auth/
│   ├── home/
│   ├── quiz/
│   └── profile/
├── widgets/              # 自定义组件
│   ├── custom_button.dart
│   └── custom_text_field.dart
└── utils/                # 工具类
    └── app_theme.dart
```

## 设计风格
- **色彩方案**: 薰衣草紫(#E6D4EF)、薄荷绿(#D1E5E9)、浅橙色
- **UI风格**: 大圆角、柔和阴影、卡片式布局
- **字体**: 无衬线字体，友好圆润
- **布局**: 移动优先设计，单列布局

## 开发环境
- Flutter SDK 3.x
- Dart 3.x
- iOS 12.0+
- Xcode 14+

## 快速开始

### 1. 安装依赖
```bash
flutter pub get
```

### 2. 运行应用
```bash
# iOS模拟器
flutter run

# 指定设备
flutter run -d <device_id>
```

### 3. 构建应用
```bash
# iOS
flutter build ios

# 生成IPA
flutter build ipa
```

## API配置
默认API地址: `http://localhost:8080/quiz-api/api`

修改API地址请编辑 `lib/services/api_service.dart` 文件中的 `baseUrl` 常量。

## 已实现功能
- ✅ 应用启动页面
- ✅ 用户认证（登录/注册）
- ✅ 首页布局和导航
- ✅ 个人中心页面
- ✅ 自定义主题和组件
- ✅ 状态管理架构
- ✅ 网络请求封装

## 待开发功能
- [ ] 测评列表和详情页面
- [ ] 答题界面实现
- [ ] 结果展示和分析
- [ ] 历史记录管理
- [ ] 收藏功能
- [ ] 分享功能
- [ ] 推送通知
- [ ] 离线缓存

## 注意事项
1. 确保后台服务正常运行
2. 网络请求需要真机或配置代理
3. iOS需要配置开发者证书
4. 图片资源需要添加到pubspec.yaml

## 开发指南
1. 遵循Flutter官方代码规范
2. 使用Provider进行状态管理
3. 网络请求统一使用ApiService
4. UI组件优先使用自定义组件
5. 颜色和样式使用AppTheme定义

## 测试
```bash
# 运行单元测试
flutter test

# 运行集成测试
flutter drive --target=test_driver/app.dart
```
