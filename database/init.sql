-- QUIZ 心理测评系统数据库初始化脚本
-- 数据库名称: quiz
-- 用户: root
-- 密码: SD0916sd!

-- 创建数据库
CREATE DATABASE IF NOT EXISTS quiz CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE quiz;

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT COMMENT '性别: 0-未知, 1-男, 2-女',
    birth_date DATE COMMENT '出生日期',
    is_vip BOOLEAN DEFAULT FALSE COMMENT '是否VIP会员',
    vip_expire_time DATETIME COMMENT 'VIP过期时间',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone)
) COMMENT '用户表';

-- 测评分类表
CREATE TABLE quiz_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    icon_url VARCHAR(500) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '测评分类表';

-- 测评表
CREATE TABLE quizzes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '测评标题',
    subtitle VARCHAR(500) COMMENT '副标题',
    description TEXT COMMENT '测评描述',
    category_id INT COMMENT '分类ID',
    cover_image VARCHAR(500) COMMENT '封面图片',
    question_count INT DEFAULT 0 COMMENT '题目数量',
    estimated_time INT DEFAULT 0 COMMENT '预估完成时间(分钟)',
    difficulty_level TINYINT DEFAULT 1 COMMENT '难度等级: 1-简单, 2-中等, 3-困难',
    is_free BOOLEAN DEFAULT TRUE COMMENT '是否免费',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    view_count BIGINT DEFAULT 0 COMMENT '浏览次数',
    take_count BIGINT DEFAULT 0 COMMENT '参与次数',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-下架, 1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES quiz_categories(id),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured)
) COMMENT '测评表';

-- 题目表
CREATE TABLE questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    question_text TEXT NOT NULL COMMENT '题目内容',
    question_type TINYINT DEFAULT 1 COMMENT '题目类型: 1-单选, 2-多选, 3-滑动打分',
    image_url VARCHAR(500) COMMENT '题目图片',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必答',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_sort (sort_order)
) COMMENT '题目表';

-- 题目选项表
CREATE TABLE question_options (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question_id BIGINT NOT NULL COMMENT '题目ID',
    option_text TEXT NOT NULL COMMENT '选项内容',
    option_value VARCHAR(50) COMMENT '选项值',
    score INT DEFAULT 0 COMMENT '选项分数',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    INDEX idx_question (question_id),
    INDEX idx_sort (sort_order)
) COMMENT '题目选项表';

-- 测评结果模板表
CREATE TABLE quiz_result_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    result_type VARCHAR(50) NOT NULL COMMENT '结果类型标识',
    title VARCHAR(200) NOT NULL COMMENT '结果标题',
    description TEXT COMMENT '结果描述',
    detailed_analysis TEXT COMMENT '详细分析',
    suggestions TEXT COMMENT '建议',
    min_score INT COMMENT '最低分数',
    max_score INT COMMENT '最高分数',
    image_url VARCHAR(500) COMMENT '结果图片',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_quiz (quiz_id),
    INDEX idx_type (result_type)
) COMMENT '测评结果模板表';

-- 用户测评记录表
CREATE TABLE user_quiz_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    total_score INT DEFAULT 0 COMMENT '总分',
    result_type VARCHAR(50) COMMENT '结果类型',
    status TINYINT DEFAULT 0 COMMENT '状态: 0-进行中, 1-已完成, 2-已放弃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_quiz (quiz_id),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
) COMMENT '用户测评记录表';

-- 用户答案表
CREATE TABLE user_answers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    record_id BIGINT NOT NULL COMMENT '测评记录ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    option_id BIGINT COMMENT '选项ID(单选/多选)',
    answer_text TEXT COMMENT '答案文本',
    score INT DEFAULT 0 COMMENT '得分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES user_quiz_records(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    FOREIGN KEY (option_id) REFERENCES question_options(id) ON DELETE CASCADE,
    INDEX idx_record (record_id),
    INDEX idx_question (question_id)
) COMMENT '用户答案表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (config_key)
) COMMENT '系统配置表';

-- 用户收藏表
CREATE TABLE user_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    quiz_id BIGINT NOT NULL COMMENT '测评ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_quiz (user_id, quiz_id),
    INDEX idx_user (user_id),
    INDEX idx_quiz (quiz_id)
) COMMENT '用户收藏表';

-- 插入初始数据
INSERT INTO quiz_categories (name, description, icon_url, sort_order) VALUES
('人格测试', 'MBTI、DISC等经典人格测评', '/icons/personality.png', 1),
('情商测试', '情绪智力和社交能力测评', '/icons/eq.png', 2),
('职业测试', '职业兴趣和能力倾向测评', '/icons/career.png', 3),
('恋爱测试', '恋爱观和情感能力测评', '/icons/love.png', 4),
('心理健康', '心理状态和健康水平测评', '/icons/mental.png', 5);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('app_name', 'QUIZ心理测评', '应用名称'),
('app_version', '1.0.0', '应用版本'),
('vip_price_monthly', '19.9', 'VIP月费价格'),
('vip_price_yearly', '199.9', 'VIP年费价格'),
('max_free_tests_per_day', '3', '每日免费测试次数限制');

-- 插入测试测评数据
INSERT INTO quizzes (title, subtitle, description, category_id, question_count, estimated_time, difficulty_level, is_free, is_featured, status) VALUES
('MBTI人格测试', '16型人格测评', '通过科学的心理学测评，了解你的人格类型和行为偏好', 1, 60, 15, 2, TRUE, TRUE, 1),
('情商测试', 'EQ情绪智力测评', '测试你的情绪管理和社交能力水平', 2, 40, 10, 1, TRUE, TRUE, 1),
('职业兴趣测试', '霍兰德职业兴趣测评', '发现你的职业兴趣和适合的工作类型', 3, 30, 8, 1, TRUE, FALSE, 1),
('恋爱人格测试', '恋爱观和情感能力测评', '了解你在恋爱关系中的表现和需求', 4, 25, 6, 1, TRUE, TRUE, 1),
('心理健康测试', '心理状态评估', '评估你当前的心理健康状况和压力水平', 5, 35, 12, 2, FALSE, FALSE, 1);

-- 插入测试题目数据（MBTI测试的前几道题目）
INSERT INTO questions (quiz_id, question_text, question_type, sort_order, is_required) VALUES
(1, '在聚会中，你更倾向于：', 1, 1, TRUE),
(1, '当面对新的挑战时，你通常：', 1, 2, TRUE),
(1, '你更喜欢：', 1, 3, TRUE),
(1, '在做决定时，你更依赖：', 1, 4, TRUE),
(1, '你的工作风格更偏向于：', 1, 5, TRUE);

-- 插入测试选项数据
INSERT INTO question_options (question_id, option_text, option_value, score, sort_order) VALUES
-- 第1题选项
(1, '主动与很多人交谈', 'E', 1, 1),
(1, '与少数几个熟悉的人深入交流', 'I', 1, 2),
-- 第2题选项
(2, '兴奋地迎接挑战', 'E', 1, 1),
(2, '仔细思考后再行动', 'I', 1, 2),
-- 第3题选项
(3, '关注具体的事实和细节', 'S', 1, 1),
(3, '关注可能性和整体概念', 'N', 1, 2),
-- 第4题选项
(4, '逻辑分析和客观事实', 'T', 1, 1),
(4, '个人价值观和他人感受', 'F', 1, 2),
-- 第5题选项
(5, '有计划、有条理地进行', 'J', 1, 1),
(5, '保持灵活性，随机应变', 'P', 1, 2);

-- 插入测评结果模板
INSERT INTO quiz_result_templates (quiz_id, result_type, title, description, detailed_analysis, suggestions, min_score, max_score) VALUES
(1, 'INTJ', '建筑师型人格', '你是一个独立、有创造力的思考者，善于制定长远计划。',
'INTJ型人格的人通常具有强烈的独立性和创造力。他们善于看到事物的整体图景，并制定实现目标的详细计划。在工作中，他们追求效率和创新，不喜欢被繁琐的规则束缚。',
'建议发挥你的战略思维优势，在需要长期规划的领域发展。同时要注意与他人的沟通，学会更好地表达自己的想法。', 0, 100),

(1, 'ENFP', '竞选者型人格', '你是一个热情、有创造力的自由精神，总能找到理由微笑。',
'ENFP型人格的人充满热情和创造力，他们善于激励他人，总是能看到事物积极的一面。他们喜欢探索新的可能性，不喜欢被限制在固定的框架中。',
'建议利用你的人际交往能力和创造力，在需要创新和团队合作的环境中发展。要注意保持专注，避免同时进行太多项目。', 0, 100),

(2, 'HIGH_EQ', '高情商', '你具有很强的情绪管理和社交能力。',
'你能够很好地理解和管理自己的情绪，同时也能敏锐地察觉他人的情感变化。在人际交往中，你表现出色，能够建立良好的关系。',
'继续发挥你的情商优势，可以考虑在需要人际交往和团队协作的领域发展。', 80, 100),

(2, 'MEDIUM_EQ', '中等情商', '你的情商处于正常水平，还有提升空间。',
'你在情绪管理和社交方面表现一般，在某些情况下可能会遇到挑战。通过学习和练习，你可以进一步提升自己的情商水平。',
'建议多关注自己和他人的情绪变化，学习更好的沟通技巧和情绪管理方法。', 40, 79);
