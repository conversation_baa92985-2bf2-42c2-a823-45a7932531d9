package com.quiz.servlet;

import com.quiz.dao.QuizDao;
import com.quiz.dao.UserQuizRecordDao;
import com.quiz.model.Quiz;
import com.quiz.model.Question;
import com.quiz.model.UserQuizRecord;
import com.quiz.util.JsonUtil;
import com.quiz.util.JwtUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测评管理Servlet
 * 处理测评相关的请求
 */
public class QuizServlet extends HttpServlet {
    private QuizDao quizDao;
    private UserQuizRecordDao recordDao;

    @Override
    public void init() throws ServletException {
        super.init();
        quizDao = new QuizDao();
        recordDao = new UserQuizRecordDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }
        
        try {
            if (pathInfo.equals("/list")) {
                handleGetQuizList(request, response);
            } else if (pathInfo.equals("/featured")) {
                handleGetFeaturedQuizzes(request, response);
            } else if (pathInfo.matches("/\\d+")) {
                // 获取测评详情 /api/quiz/123
                Long quizId = Long.parseLong(pathInfo.substring(1));
                handleGetQuizDetail(request, response, quizId);
            } else if (pathInfo.matches("/\\d+/questions")) {
                // 获取测评题目 /api/quiz/123/questions
                String[] parts = pathInfo.split("/");
                Long quizId = Long.parseLong(parts[1]);
                handleGetQuizQuestions(request, response, quizId);
            } else {
                JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            JsonUtil.sendBadRequest(response, "缺少请求路径");
            return;
        }

        try {
            if (pathInfo.matches("/\\d+/start")) {
                // 开始测评 /api/quiz/123/start
                String[] parts = pathInfo.split("/");
                Long quizId = Long.parseLong(parts[1]);
                handleStartQuiz(request, response, quizId);
            } else {
                JsonUtil.sendNotFound(response, "未找到请求的接口");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsonUtil.sendError(response, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 开始测评
     */
    private void handleStartQuiz(HttpServletRequest request, HttpServletResponse response, Long quizId)
            throws IOException, SQLException {

        // 手动检查认证
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            JsonUtil.sendUnauthorized(response, "缺少认证token");
            return;
        }

        // 验证token并获取用户ID
        String token = authHeader.substring(7);
        Long userId = JwtUtil.validateToken(token);
        if (userId == null) {
            JsonUtil.sendUnauthorized(response, "无效的认证token");
            return;
        }

        // 检查测评是否存在
        Quiz quiz = quizDao.getQuizById(quizId);
        if (quiz == null) {
            JsonUtil.sendNotFound(response, "测评不存在");
            return;
        }

        // 创建测评记录
        UserQuizRecord record = new UserQuizRecord(userId, quizId);
        Long recordId = recordDao.createRecord(record);

        // 增加测评参与次数
        quizDao.incrementTakeCount(quizId);

        Map<String, Object> result = new HashMap<>();
        result.put("recordId", recordId);
        result.put("quizId", quizId);
        result.put("quizTitle", quiz.getTitle());
        result.put("questionCount", quiz.getQuestionCount());

        JsonUtil.sendSuccess(response, result);
    }

    /**
     * 获取测评列表
     */
    private void handleGetQuizList(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        // 获取查询参数
        String categoryParam = request.getParameter("category");
        String pageParam = request.getParameter("page");
        String pageSizeParam = request.getParameter("pageSize");
        
        Integer categoryId = null;
        if (categoryParam != null && !categoryParam.trim().isEmpty()) {
            try {
                categoryId = Integer.parseInt(categoryParam);
            } catch (NumberFormatException e) {
                JsonUtil.sendBadRequest(response, "分类ID格式错误");
                return;
            }
        }
        
        int page = 1;
        if (pageParam != null && !pageParam.trim().isEmpty()) {
            try {
                page = Integer.parseInt(pageParam);
                if (page < 1) page = 1;
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        
        int pageSize = 20;
        if (pageSizeParam != null && !pageSizeParam.trim().isEmpty()) {
            try {
                pageSize = Integer.parseInt(pageSizeParam);
                if (pageSize < 1) pageSize = 20;
                if (pageSize > 100) pageSize = 100; // 限制最大页面大小
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        
        int offset = (page - 1) * pageSize;
        
        List<Quiz> quizzes = quizDao.getQuizzes(categoryId, null, offset, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        result.put("list", quizzes);
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", quizzes.size()); // 简化实现，实际应该查询总数
        
        JsonUtil.sendSuccess(response, result);
    }
    
    /**
     * 获取推荐测评
     */
    private void handleGetFeaturedQuizzes(HttpServletRequest request, HttpServletResponse response)
            throws IOException, SQLException {
        
        List<Quiz> featuredQuizzes = quizDao.getQuizzes(null, true, 0, 10);
        JsonUtil.sendSuccess(response, featuredQuizzes);
    }
    
    /**
     * 获取测评详情
     */
    private void handleGetQuizDetail(HttpServletRequest request, HttpServletResponse response, Long quizId)
            throws IOException, SQLException {
        
        Quiz quiz = quizDao.getQuizById(quizId);
        if (quiz == null) {
            JsonUtil.sendNotFound(response, "测评不存在");
            return;
        }
        
        // 增加浏览次数
        quizDao.incrementViewCount(quizId);
        
        JsonUtil.sendSuccess(response, quiz);
    }
    
    /**
     * 获取测评题目
     */
    private void handleGetQuizQuestions(HttpServletRequest request, HttpServletResponse response, Long quizId)
            throws IOException, SQLException {
        
        // 先检查测评是否存在
        Quiz quiz = quizDao.getQuizById(quizId);
        if (quiz == null) {
            JsonUtil.sendNotFound(response, "测评不存在");
            return;
        }
        
        // 获取题目列表
        List<Question> questions = quizDao.getQuizQuestions(quizId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("quizId", quizId);
        result.put("quizTitle", quiz.getTitle());
        result.put("questions", questions);
        result.put("totalQuestions", questions.size());
        
        JsonUtil.sendSuccess(response, result);
    }
}
